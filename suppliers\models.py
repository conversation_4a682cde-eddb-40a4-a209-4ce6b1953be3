from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator, MinValueValidator
from decimal import Decimal


class Supplier(models.Model):
    """
    Supplier/Vendor model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم المورد')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رمز المورد')
    )

    contact_person = models.CharField(
        max_length=100,
        verbose_name=_('الشخص المسؤول')
    )

    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message=_("رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    )

    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        verbose_name=_('رقم الهاتف')
    )

    email = models.EmailField(
        blank=True,
        verbose_name=_('البريد الإلكتروني')
    )

    address = models.TextField(
        verbose_name=_('العنوان')
    )

    city = models.CharField(
        max_length=50,
        verbose_name=_('المدينة')
    )

    country = models.CharField(
        max_length=50,
        verbose_name=_('البلد')
    )

    # Financial details
    credit_limit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('حد الائتمان')
    )

    payment_terms = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('شروط الدفع')
    )

    tax_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('الرقم الضريبي')
    )

    # Status
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('مورد')
        verbose_name_plural = _('الموردون')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def total_purchases(self):
        """Calculate total purchases from this supplier"""
        return self.purchase_orders.filter(
            status='completed'
        ).aggregate(
            total=models.Sum('total_amount')
        )['total'] or Decimal('0.00')

    @property
    def outstanding_balance(self):
        """Calculate outstanding balance"""
        return self.purchase_orders.filter(
            status='completed',
            payment_status__in=['pending', 'partial']
        ).aggregate(
            total=models.Sum('total_amount')
        )['total'] or Decimal('0.00')


class PurchaseOrder(models.Model):
    """
    Purchase order model
    """
    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('sent', _('مرسل')),
        ('confirmed', _('مؤكد')),
        ('received', _('مستلم')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('partial', _('جزئي')),
        ('paid', _('مدفوع')),
    ]

    order_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم الطلب')
    )

    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name='purchase_orders',
        verbose_name=_('المورد')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.PROTECT,
        related_name='purchase_orders',
        verbose_name=_('الفرع')
    )

    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        related_name='created_purchase_orders',
        verbose_name=_('أنشئ بواسطة')
    )

    order_date = models.DateField(
        verbose_name=_('تاريخ الطلب')
    )

    expected_delivery_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ التسليم المتوقع')
    )

    actual_delivery_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ التسليم الفعلي')
    )

    # Financial details
    subtotal = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المجموع الفرعي')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name=_('مبلغ الخصم')
    )

    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('مبلغ الضريبة')
    )

    shipping_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name=_('تكلفة الشحن')
    )

    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المبلغ الإجمالي')
    )

    paid_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المبلغ المدفوع')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_('الحالة')
    )

    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='pending',
        verbose_name=_('حالة الدفع')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('أمر شراء')
        verbose_name_plural = _('أوامر الشراء')
        ordering = ['-created_at']

    def __str__(self):
        return f"أمر شراء {self.order_number} - {self.supplier.name}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()

        # Calculate totals
        self.calculate_totals()

        super().save(*args, **kwargs)

    def generate_order_number(self):
        """Generate unique order number"""
        from django.utils import timezone
        today = timezone.now().strftime('%Y%m%d')
        last_order = PurchaseOrder.objects.filter(
            order_number__startswith=f"PO{today}"
        ).order_by('-order_number').first()

        if last_order:
            last_number = int(last_order.order_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"PO{today}{new_number:04d}"

    def calculate_totals(self):
        """Calculate order totals"""
        # Calculate subtotal from items
        self.subtotal = sum(
            item.total_cost for item in self.items.all()
        )

        # Calculate tax (15% default)
        taxable_amount = self.subtotal - self.discount_amount
        self.tax_amount = (taxable_amount * Decimal('15.00')) / 100

        # Calculate total
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount + self.shipping_cost

    @property
    def remaining_balance(self):
        """Calculate remaining balance"""
        return self.total_amount - self.paid_amount


class PurchaseOrderItem(models.Model):
    """
    Individual items in a purchase order
    """
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('أمر الشراء')
    )

    medicine = models.ForeignKey(
        'inventory.Medicine',
        on_delete=models.PROTECT,
        verbose_name=_('الدواء')
    )

    quantity_ordered = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name=_('الكمية المطلوبة')
    )

    quantity_received = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name=_('الكمية المستلمة')
    )

    unit_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('تكلفة الوحدة')
    )

    total_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('التكلفة الإجمالية')
    )

    # Batch information for received items
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('رقم الدفعة')
    )

    manufacturing_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ التصنيع')
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ انتهاء الصلاحية')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('عنصر أمر الشراء')
        verbose_name_plural = _('عناصر أمر الشراء')

    def __str__(self):
        return f"{self.medicine.name} - {self.quantity_ordered}"

    def save(self, *args, **kwargs):
        # Calculate total cost
        self.total_cost = self.quantity_ordered * self.unit_cost
        super().save(*args, **kwargs)

    @property
    def is_fully_received(self):
        return self.quantity_received >= self.quantity_ordered

    @property
    def remaining_quantity(self):
        return max(0, self.quantity_ordered - self.quantity_received)


class Payment(models.Model):
    """
    Supplier payments model
    """
    PAYMENT_METHODS = [
        ('cash', _('نقدي')),
        ('bank_transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('credit_card', _('بطاقة ائتمان')),
    ]

    payment_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم الدفعة')
    )

    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name='payments',
        verbose_name=_('المورد')
    )

    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.PROTECT,
        related_name='payments',
        verbose_name=_('أمر الشراء')
    )

    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('المبلغ')
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        verbose_name=_('طريقة الدفع')
    )

    payment_date = models.DateField(
        verbose_name=_('تاريخ الدفع')
    )

    reference_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('رقم المرجع')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    processed_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name=_('تم بواسطة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('الدفعات')
        ordering = ['-payment_date']

    def __str__(self):
        return f"دفعة {self.payment_number} - {self.supplier.name}"

    def save(self, *args, **kwargs):
        if not self.payment_number:
            self.payment_number = self.generate_payment_number()
        super().save(*args, **kwargs)

    def generate_payment_number(self):
        """Generate unique payment number"""
        from django.utils import timezone
        today = timezone.now().strftime('%Y%m%d')
        last_payment = Payment.objects.filter(
            payment_number__startswith=f"PAY{today}"
        ).order_by('-payment_number').first()

        if last_payment:
            last_number = int(last_payment.payment_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"PAY{today}{new_number:04d}"


class SupplierOffer(models.Model):
    """
    Special offers from suppliers
    """
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name='offers',
        verbose_name=_('المورد')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان العرض')
    )

    description = models.TextField(
        verbose_name=_('وصف العرض')
    )

    start_date = models.DateField(
        verbose_name=_('تاريخ البداية')
    )

    end_date = models.DateField(
        verbose_name=_('تاريخ النهاية')
    )

    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name=_('نسبة الخصم')
    )

    minimum_quantity = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        verbose_name=_('الحد الأدنى للكمية')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('عرض مورد')
        verbose_name_plural = _('عروض الموردين')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.title} - {self.supplier.name}"

    @property
    def is_valid(self):
        """Check if offer is currently valid"""
        from django.utils import timezone
        today = timezone.now().date()
        return self.is_active and self.start_date <= today <= self.end_date


class SupplierOfferItem(models.Model):
    """
    Items included in supplier offers
    """
    offer = models.ForeignKey(
        SupplierOffer,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('العرض')
    )

    medicine = models.ForeignKey(
        'inventory.Medicine',
        on_delete=models.CASCADE,
        verbose_name=_('الدواء')
    )

    special_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('السعر الخاص')
    )

    class Meta:
        verbose_name = _('عنصر عرض المورد')
        verbose_name_plural = _('عناصر عروض الموردين')
        unique_together = ['offer', 'medicine']

    def __str__(self):
        return f"{self.medicine.name} - {self.special_price}"
