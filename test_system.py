#!/usr/bin/env python
"""
سكريبت فحص شامل لنظام إدارة الصيدليات
Comprehensive testing script for Pharmacy Management System
"""

import requests
import json
from bs4 import BeautifulSoup
import time

class PharmacySystemTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        self.test_results = []
        
    def log_test(self, test_name, status, details=""):
        """تسجيل نتيجة الاختبار"""
        result = {
            'test': test_name,
            'status': status,
            'details': details,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   {details}")
    
    def get_csrf_token(self, url):
        """الحصول على CSRF token"""
        try:
            response = self.session.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
            if csrf_input:
                return csrf_input.get('value')
        except Exception as e:
            print(f"خطأ في الحصول على CSRF token: {e}")
        return None
    
    def test_basic_urls(self):
        """فحص URLs الأساسية"""
        print("\n🔍 فحص URLs الأساسية...")
        
        urls_to_test = [
            ('/', 'الصفحة الرئيسية'),
            ('/login/', 'صفحة تسجيل الدخول'),
            ('/admin/', 'لوحة الإدارة'),
            ('/dashboard/', 'لوحة التحكم (محمية)'),
            ('/pos/', 'نقاط البيع (محمية)'),
            ('/profile/', 'الملف الشخصي (محمي)'),
            ('/search/', 'البحث (محمي)'),
            ('/notifications/', 'الإشعارات (محمية)'),
        ]
        
        for url, name in urls_to_test:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if url == '/login/':
                    # صفحة تسجيل الدخول يجب أن تعطي 200
                    expected_status = 200
                else:
                    # الصفحات الأخرى قد تعطي 302 (redirect) أو 200
                    expected_status = [200, 302]
                
                if isinstance(expected_status, list):
                    status = "PASS" if response.status_code in expected_status else "FAIL"
                else:
                    status = "PASS" if response.status_code == expected_status else "FAIL"
                
                self.log_test(
                    f"URL: {name}",
                    status,
                    f"Status Code: {response.status_code}"
                )
            except Exception as e:
                self.log_test(f"URL: {name}", "FAIL", f"خطأ: {str(e)}")
    
    def test_login_functionality(self):
        """فحص وظيفة تسجيل الدخول"""
        print("\n🔐 فحص وظيفة تسجيل الدخول...")
        
        # الحصول على صفحة تسجيل الدخول
        try:
            login_url = f"{self.base_url}/login/"
            response = self.session.get(login_url)
            
            if response.status_code == 200:
                self.log_test("الوصول لصفحة تسجيل الدخول", "PASS", "تم تحميل الصفحة بنجاح")
                
                # البحث عن نموذج تسجيل الدخول
                soup = BeautifulSoup(response.content, 'html.parser')
                form = soup.find('form')
                username_field = soup.find('input', {'name': 'username'})
                password_field = soup.find('input', {'name': 'password'})
                csrf_token = soup.find('input', {'name': 'csrfmiddlewaretoken'})
                
                if form and username_field and password_field and csrf_token:
                    self.log_test("عناصر نموذج تسجيل الدخول", "PASS", "جميع الحقول موجودة")
                    self.csrf_token = csrf_token.get('value')
                    
                    # اختبار تسجيل الدخول
                    login_data = {
                        'username': 'admin',
                        'password': 'admin123',
                        'csrfmiddlewaretoken': self.csrf_token
                    }
                    
                    login_response = self.session.post(login_url, data=login_data)
                    
                    if login_response.status_code == 302:
                        self.log_test("تسجيل الدخول", "PASS", "تم تسجيل الدخول بنجاح")
                        return True
                    else:
                        self.log_test("تسجيل الدخول", "FAIL", f"Status: {login_response.status_code}")
                        return False
                else:
                    self.log_test("عناصر نموذج تسجيل الدخول", "FAIL", "حقول مفقودة")
                    return False
            else:
                self.log_test("الوصول لصفحة تسجيل الدخول", "FAIL", f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("تسجيل الدخول", "FAIL", f"خطأ: {str(e)}")
            return False
    
    def test_protected_pages(self):
        """فحص الصفحات المحمية بعد تسجيل الدخول"""
        print("\n🛡️ فحص الصفحات المحمية...")
        
        protected_urls = [
            ('/dashboard/', 'لوحة التحكم'),
            ('/pos/', 'قائمة المبيعات'),
            ('/pos/new-sale/', 'بيع جديد'),
            ('/pos/cash-register/', 'إدارة صندوق النقد'),
            ('/profile/', 'الملف الشخصي'),
            ('/search/', 'البحث'),
            ('/notifications/', 'الإشعارات'),
        ]
        
        for url, name in protected_urls:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                
                if response.status_code == 200:
                    self.log_test(f"صفحة محمية: {name}", "PASS", "تم الوصول بنجاح")
                    
                    # فحص وجود المحتوى المتوقع
                    if 'نظام إدارة الصيدليات' in response.text:
                        self.log_test(f"محتوى صفحة: {name}", "PASS", "المحتوى صحيح")
                    else:
                        self.log_test(f"محتوى صفحة: {name}", "WARN", "المحتوى قد يكون ناقص")
                        
                elif response.status_code == 302:
                    self.log_test(f"صفحة محمية: {name}", "WARN", "تم التوجيه (ربما لصفحة أخرى)")
                else:
                    self.log_test(f"صفحة محمية: {name}", "FAIL", f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"صفحة محمية: {name}", "FAIL", f"خطأ: {str(e)}")
    
    def test_api_endpoints(self):
        """فحص API endpoints"""
        print("\n🔌 فحص API endpoints...")
        
        api_urls = [
            ('/api/quick-stats/', 'إحصائيات سريعة'),
            ('/pos/api/search-medicine/?q=test', 'البحث عن الأدوية'),
        ]
        
        for url, name in api_urls:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                
                if response.status_code == 200:
                    try:
                        # محاولة تحليل JSON
                        json_data = response.json()
                        self.log_test(f"API: {name}", "PASS", "استجابة JSON صحيحة")
                    except:
                        self.log_test(f"API: {name}", "WARN", "الاستجابة ليست JSON")
                elif response.status_code == 302:
                    self.log_test(f"API: {name}", "WARN", "تم التوجيه")
                else:
                    self.log_test(f"API: {name}", "FAIL", f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"API: {name}", "FAIL", f"خطأ: {str(e)}")
    
    def test_static_files(self):
        """فحص الملفات الثابتة"""
        print("\n📁 فحص الملفات الثابتة...")
        
        # فحص تحميل CSS و JS من CDN
        static_resources = [
            ('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css', 'Bootstrap CSS'),
            ('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', 'Font Awesome'),
            ('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap', 'Google Fonts'),
        ]
        
        for url, name in static_resources:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    self.log_test(f"مورد خارجي: {name}", "PASS", "متاح")
                else:
                    self.log_test(f"مورد خارجي: {name}", "FAIL", f"Status: {response.status_code}")
            except Exception as e:
                self.log_test(f"مورد خارجي: {name}", "FAIL", f"خطأ: {str(e)}")
    
    def generate_report(self):
        """إنشاء تقرير شامل"""
        print("\n📊 تقرير الفحص الشامل")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t['status'] == 'PASS'])
        failed_tests = len([t for t in self.test_results if t['status'] == 'FAIL'])
        warning_tests = len([t for t in self.test_results if t['status'] == 'WARN'])
        
        print(f"إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجح: {passed_tests}")
        print(f"❌ فشل: {failed_tests}")
        print(f"⚠️ تحذير: {warning_tests}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"معدل النجاح: {success_rate:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ الاختبارات الفاشلة:")
            for test in self.test_results:
                if test['status'] == 'FAIL':
                    print(f"  - {test['test']}: {test['details']}")
        
        return {
            'total': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'warnings': warning_tests,
            'success_rate': success_rate
        }
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء الفحص الشامل لنظام إدارة الصيدليات")
        print("=" * 60)
        
        # فحص URLs الأساسية
        self.test_basic_urls()
        
        # فحص تسجيل الدخول
        login_success = self.test_login_functionality()
        
        # إذا نجح تسجيل الدخول، فحص الصفحات المحمية
        if login_success:
            self.test_protected_pages()
            self.test_api_endpoints()
        
        # فحص الملفات الثابتة
        self.test_static_files()
        
        # إنشاء التقرير
        return self.generate_report()

def main():
    """الدالة الرئيسية"""
    tester = PharmacySystemTester()
    results = tester.run_all_tests()
    
    print(f"\n🎯 النتيجة النهائية: {results['success_rate']:.1f}% نجاح")
    
    if results['success_rate'] >= 90:
        print("🎉 النظام يعمل بشكل ممتاز!")
    elif results['success_rate'] >= 75:
        print("👍 النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة")
    else:
        print("⚠️ النظام يحتاج إلى مراجعة وإصلاحات")

if __name__ == "__main__":
    main()
