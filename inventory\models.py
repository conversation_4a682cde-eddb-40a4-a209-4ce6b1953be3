from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from decimal import Decimal


class Category(models.Model):
    """
    Medicine/Product categories
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الفئة')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رمز الفئة')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('الوصف')
    )

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='subcategories',
        verbose_name=_('الفئة الأب')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('فئة')
        verbose_name_plural = _('الفئات')
        ordering = ['name']

    def __str__(self):
        return self.name


class Manufacturer(models.Model):
    """
    Medicine manufacturers/companies
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الشركة')
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رمز الشركة')
    )

    country = models.CharField(
        max_length=50,
        verbose_name=_('البلد')
    )

    contact_info = models.TextField(
        blank=True,
        verbose_name=_('معلومات الاتصال')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('شركة تصنيع')
        verbose_name_plural = _('شركات التصنيع')
        ordering = ['name']

    def __str__(self):
        return self.name


class Medicine(models.Model):
    """
    Medicine/Product master data
    """
    UNIT_CHOICES = [
        ('tablet', _('قرص')),
        ('capsule', _('كبسولة')),
        ('bottle', _('زجاجة')),
        ('box', _('علبة')),
        ('tube', _('أنبوب')),
        ('vial', _('قارورة')),
        ('sachet', _('كيس')),
        ('piece', _('قطعة')),
    ]

    name = models.CharField(
        max_length=200,
        verbose_name=_('اسم الدواء')
    )

    generic_name = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('الاسم العلمي')
    )

    barcode = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('الباركود')
    )

    category = models.ForeignKey(
        Category,
        on_delete=models.PROTECT,
        related_name='medicines',
        verbose_name=_('الفئة')
    )

    manufacturer = models.ForeignKey(
        Manufacturer,
        on_delete=models.PROTECT,
        related_name='medicines',
        verbose_name=_('الشركة المصنعة')
    )

    unit = models.CharField(
        max_length=20,
        choices=UNIT_CHOICES,
        default='tablet',
        verbose_name=_('الوحدة')
    )

    strength = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('التركيز')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('الوصف')
    )

    # Pricing
    cost_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('سعر التكلفة')
    )

    selling_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('سعر البيع')
    )

    # Stock management
    minimum_stock = models.IntegerField(
        default=10,
        validators=[MinValueValidator(0)],
        verbose_name=_('الحد الأدنى للمخزون')
    )

    maximum_stock = models.IntegerField(
        default=1000,
        validators=[MinValueValidator(1)],
        verbose_name=_('الحد الأقصى للمخزون')
    )

    # Medicine specific
    requires_prescription = models.BooleanField(
        default=False,
        verbose_name=_('يتطلب وصفة طبية')
    )

    is_controlled = models.BooleanField(
        default=False,
        verbose_name=_('دواء مراقب')
    )

    storage_conditions = models.TextField(
        blank=True,
        verbose_name=_('شروط التخزين')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('دواء')
        verbose_name_plural = _('الأدوية')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.strength}"

    @property
    def current_stock(self):
        """Calculate current total stock across all batches"""
        return sum(batch.current_quantity for batch in self.batches.filter(
            current_quantity__gt=0,
            expiry_date__gt=models.functions.Now()
        ))

    @property
    def profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0


class Batch(models.Model):
    """
    Medicine batches with expiry dates and quantities
    """
    medicine = models.ForeignKey(
        Medicine,
        on_delete=models.CASCADE,
        related_name='batches',
        verbose_name=_('الدواء')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.CASCADE,
        related_name='batches',
        verbose_name=_('الفرع')
    )

    batch_number = models.CharField(
        max_length=50,
        verbose_name=_('رقم الدفعة')
    )

    manufacturing_date = models.DateField(
        verbose_name=_('تاريخ التصنيع')
    )

    expiry_date = models.DateField(
        verbose_name=_('تاريخ انتهاء الصلاحية')
    )

    initial_quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name=_('الكمية الأولية')
    )

    current_quantity = models.IntegerField(
        validators=[MinValueValidator(0)],
        verbose_name=_('الكمية الحالية')
    )

    cost_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('سعر التكلفة')
    )

    selling_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('سعر البيع')
    )

    supplier = models.ForeignKey(
        'suppliers.Supplier',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('المورد')
    )

    purchase_order = models.ForeignKey(
        'suppliers.PurchaseOrder',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('أمر الشراء')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('الدفعات')
        ordering = ['expiry_date']
        unique_together = ['medicine', 'branch', 'batch_number']

    def __str__(self):
        return f"{self.medicine.name} - {self.batch_number}"

    @property
    def is_expired(self):
        """Check if batch is expired"""
        from django.utils import timezone
        return self.expiry_date < timezone.now().date()

    @property
    def days_to_expiry(self):
        """Calculate days until expiry"""
        from django.utils import timezone
        delta = self.expiry_date - timezone.now().date()
        return delta.days

    def save(self, *args, **kwargs):
        # Set current_quantity to initial_quantity if not set
        if not self.pk and not self.current_quantity:
            self.current_quantity = self.initial_quantity
        super().save(*args, **kwargs)


class StockMovement(models.Model):
    """
    Track all stock movements (in/out)
    """
    MOVEMENT_TYPES = [
        ('purchase', _('شراء')),
        ('sale', _('بيع')),
        ('return', _('إرجاع')),
        ('transfer_in', _('تحويل وارد')),
        ('transfer_out', _('تحويل صادر')),
        ('adjustment', _('تسوية')),
        ('expired', _('منتهي الصلاحية')),
        ('damaged', _('تالف')),
    ]

    batch = models.ForeignKey(
        Batch,
        on_delete=models.CASCADE,
        related_name='movements',
        verbose_name=_('الدفعة')
    )

    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPES,
        verbose_name=_('نوع الحركة')
    )

    quantity = models.IntegerField(
        verbose_name=_('الكمية')
    )

    # Positive for incoming, negative for outgoing
    quantity_change = models.IntegerField(
        verbose_name=_('تغيير الكمية')
    )

    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name=_('سعر الوحدة')
    )

    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('المبلغ الإجمالي')
    )

    reference_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('رقم المرجع')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    user = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name=_('المستخدم')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الحركة')
    )

    class Meta:
        verbose_name = _('حركة مخزون')
        verbose_name_plural = _('حركات المخزون')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.batch.medicine.name} - {self.get_movement_type_display()} - {self.quantity}"

    def save(self, *args, **kwargs):
        # Calculate total amount
        self.total_amount = self.quantity * self.unit_price

        # Update batch quantity
        if self.pk is None:  # New movement
            self.batch.current_quantity += self.quantity_change
            self.batch.save()

        super().save(*args, **kwargs)


class StockAlert(models.Model):
    """
    Stock alerts for low stock and expiry warnings
    """
    ALERT_TYPES = [
        ('low_stock', _('مخزون منخفض')),
        ('expiry_warning', _('تحذير انتهاء صلاحية')),
        ('expired', _('منتهي الصلاحية')),
        ('out_of_stock', _('نفاد المخزون')),
    ]

    medicine = models.ForeignKey(
        Medicine,
        on_delete=models.CASCADE,
        related_name='alerts',
        verbose_name=_('الدواء')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.CASCADE,
        related_name='stock_alerts',
        verbose_name=_('الفرع')
    )

    alert_type = models.CharField(
        max_length=20,
        choices=ALERT_TYPES,
        verbose_name=_('نوع التنبيه')
    )

    message = models.TextField(
        verbose_name=_('رسالة التنبيه')
    )

    current_stock = models.IntegerField(
        verbose_name=_('المخزون الحالي')
    )

    threshold = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_('الحد المحدد')
    )

    is_acknowledged = models.BooleanField(
        default=False,
        verbose_name=_('تم الاطلاع')
    )

    acknowledged_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('تم الاطلاع بواسطة')
    )

    acknowledged_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الاطلاع')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('تنبيه مخزون')
        verbose_name_plural = _('تنبيهات المخزون')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.medicine.name} - {self.get_alert_type_display()}"
