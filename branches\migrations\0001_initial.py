# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفرع')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز الفرع')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('phone_number', models.Char<PERSON>ield(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('license_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الترخيص')),
                ('license_expiry_date', models.DateField(verbose_name='تاريخ انتهاء الترخيص')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_main_branch', models.BooleanField(default=False, verbose_name='الفرع الرئيسي')),
                ('opening_time', models.TimeField(verbose_name='وقت الافتتاح')),
                ('closing_time', models.TimeField(verbose_name='وقت الإغلاق')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_branches', to=settings.AUTH_USER_MODEL, verbose_name='المدير')),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BranchSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=15.0, max_digits=5, verbose_name='معدل الضريبة (%)')),
                ('currency_code', models.CharField(default='SAR', max_length=3, verbose_name='رمز العملة')),
                ('currency_symbol', models.CharField(default='ر.س', max_length=5, verbose_name='رمز العملة')),
                ('receipt_header', models.TextField(blank=True, verbose_name='رأس الفاتورة')),
                ('receipt_footer', models.TextField(blank=True, verbose_name='ذيل الفاتورة')),
                ('low_stock_threshold', models.IntegerField(default=10, verbose_name='حد التنبيه للمخزون المنخفض')),
                ('expiry_alert_days', models.IntegerField(default=30, verbose_name='أيام التنبيه قبل انتهاء الصلاحية')),
                ('auto_print_receipt', models.BooleanField(default=True, verbose_name='طباعة الفاتورة تلقائياً')),
                ('allow_negative_stock', models.BooleanField(default=False, verbose_name='السماح بالمخزون السالب')),
                ('auto_backup_enabled', models.BooleanField(default=True, verbose_name='النسخ الاحتياطي التلقائي')),
                ('backup_frequency_hours', models.IntegerField(default=24, verbose_name='تكرار النسخ الاحتياطي (ساعات)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to='branches.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'إعدادات الفرع',
                'verbose_name_plural': 'إعدادات الفروع',
            },
        ),
    ]
