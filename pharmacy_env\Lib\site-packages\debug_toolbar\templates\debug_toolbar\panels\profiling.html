{% load i18n %}
<table>
  <thead>
    <tr>
      <th>{% trans "Call" %}</th>
      <th>{% trans "CumTime" %}</th>
      <th>{% trans "Per" %}</th>
      <th>{% trans "TotTime" %}</th>
      <th>{% trans "Per" %}</th>
      <th>{% trans "Count" %}</th>
    </tr>
  </thead>
  <tbody>
    {% for call in func_list %}
      <tr class="djdt-profile-row {% if call.is_project_func %}djdt-highlighted {% endif %} {% for parent_id in call.parent_ids %} djToggleDetails_{{ parent_id }}{% endfor %}" id="profilingMain_{{ call.id }}">
        <td>
          <div data-djdt-styles="paddingLeft:{{ call.indent }}px">
            {% if call.has_subfuncs %}
              <button type="button" class="djProfileToggleDetails djToggleSwitch" data-toggle-name="profilingMain" data-toggle-id="{{ call.id }}">-</button>
            {% else %}
              <span class="djNoToggleSwitch"></span>
            {% endif %}
            <span class="djdt-stack">{{ call.func_std_string }}</span>
          </div>
        </td>
        <td>{{ call.cumtime|floatformat:3 }}</td>
        <td>{{ call.cumtime_per_call|floatformat:3 }}</td>
        <td>{{ call.tottime|floatformat:3 }}</td>
        <td>{{ call.tottime_per_call|floatformat:3 }}</td>
        <td>{{ call.count }}</td>
      </tr>
    {% endfor %}
  </tbody>
</table>
