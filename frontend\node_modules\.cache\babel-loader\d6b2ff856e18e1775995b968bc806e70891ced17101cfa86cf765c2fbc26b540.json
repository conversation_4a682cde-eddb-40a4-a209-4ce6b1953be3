{"ast": null, "code": "// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize(win = window) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = win.document.documentElement.clientWidth;\n  return win.innerWidth - documentWidth;\n}", "map": {"version": 3, "names": ["getScrollbarSize", "win", "window", "documentWidth", "document", "documentElement", "clientWidth", "innerWidth"], "sources": ["D:/pos/frontend/node_modules/@mui/utils/esm/getScrollbarSize/getScrollbarSize.js"], "sourcesContent": ["// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize(win = window) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = win.document.documentElement.clientWidth;\n  return win.innerWidth - documentWidth;\n}"], "mappings": "AAAA;AACA;AACA,eAAe,SAASA,gBAAgBA,CAACC,GAAG,GAAGC,MAAM,EAAE;EACrD;EACA,MAAMC,aAAa,GAAGF,GAAG,CAACG,QAAQ,CAACC,eAAe,CAACC,WAAW;EAC9D,OAAOL,GAAG,CAACM,UAAU,GAAGJ,aAAa;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}