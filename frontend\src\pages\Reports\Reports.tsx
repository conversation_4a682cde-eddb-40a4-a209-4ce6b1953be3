import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Chip,
} from '@mui/material';
import {
  Assessment,
  Download,
  DateRange,
  TrendingUp,
  Inventory,
  People,
  AttachMoney,
  Print,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { Report } from '../../types';
import apiService from '../../services/api';

const reportTypes = [
  {
    id: 'sales',
    title: 'تقرير المبيعات',
    description: 'تقرير شامل عن المبيعات والإيرادات',
    icon: <AttachMoney />,
    color: 'success',
  },
  {
    id: 'inventory',
    title: 'تقرير المخزون',
    description: 'حالة المخزون والأصناف المنخفضة',
    icon: <Inventory />,
    color: 'warning',
  },
  {
    id: 'customers',
    title: 'تقرير العملاء',
    description: 'إحصائيات العملاء ونقاط الولاء',
    icon: <People />,
    color: 'info',
  },
  {
    id: 'financial',
    title: 'التقرير المالي',
    description: 'الأرباح والخسائر والتدفق النقدي',
    icon: <TrendingUp />,
    color: 'primary',
  },
];

export default function Reports() {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState('');
  const [reportParameters, setReportParameters] = useState({
    start_date: dayjs().subtract(30, 'day'),
    end_date: dayjs(),
    branch: '',
    category: '',
  });

  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    try {
      setLoading(true);
      const response = await apiService.getReports();
      setReports(response.results);
    } catch (err) {
      setError('فشل في تحميل التقارير');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    try {
      setLoading(true);
      const parameters = {
        ...reportParameters,
        start_date: reportParameters.start_date.format('YYYY-MM-DD'),
        end_date: reportParameters.end_date.format('YYYY-MM-DD'),
      };

      const report = await apiService.generateReport(selectedReportType, parameters);
      setDialogOpen(false);
      loadReports();
      
      // تحميل التقرير تلقائياً
      if (report.file_path) {
        handleDownloadReport(report.id);
      }
    } catch (err) {
      setError('فشل في إنشاء التقرير');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadReport = async (reportId: number) => {
    try {
      const blob = await apiService.downloadReport(reportId);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `report_${reportId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('فشل في تحميل التقرير');
    }
  };

  const handleOpenDialog = (reportType: string) => {
    setSelectedReportType(reportType);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedReportType('');
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        التقارير والتحليلات
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* أنواع التقارير */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        إنشاء تقرير جديد
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {reportTypes.map((reportType) => (
          <Grid item xs={12} sm={6} md={3} key={reportType.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      backgroundColor: `${reportType.color}.light`,
                      borderRadius: '50%',
                      p: 1,
                      mr: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    {reportType.icon}
                  </Box>
                  <Typography variant="h6">
                    {reportType.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {reportType.description}
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  variant="contained"
                  color={reportType.color as any}
                  startIcon={<Assessment />}
                  onClick={() => handleOpenDialog(reportType.id)}
                >
                  إنشاء التقرير
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* التقارير المحفوظة */}
      <Typography variant="h6" gutterBottom>
        التقارير المحفوظة
      </Typography>
      
      <Paper sx={{ p: 3 }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>اسم التقرير</TableCell>
                <TableCell>النوع</TableCell>
                <TableCell>تاريخ الإنشاء</TableCell>
                <TableCell>المنشئ</TableCell>
                <TableCell align="center">الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {reports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell>{report.name}</TableCell>
                  <TableCell>
                    <Chip
                      label={
                        reportTypes.find(type => type.id === report.type)?.title || report.type
                      }
                      color="primary"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(report.generated_at).toLocaleDateString('ar-SA')}
                  </TableCell>
                  <TableCell>{report.generated_by.first_name} {report.generated_by.last_name}</TableCell>
                  <TableCell align="center">
                    <Button
                      size="small"
                      startIcon={<Download />}
                      onClick={() => handleDownloadReport(report.id)}
                    >
                      تحميل
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Print />}
                      sx={{ ml: 1 }}
                    >
                      طباعة
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              {reports.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    لا توجد تقارير محفوظة
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* نافذة إعدادات التقرير */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          إعدادات التقرير - {reportTypes.find(type => type.id === selectedReportType)?.title}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="من تاريخ"
                value={reportParameters.start_date}
                onChange={(newValue) =>
                  setReportParameters({
                    ...reportParameters,
                    start_date: newValue || dayjs(),
                  })
                }
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="إلى تاريخ"
                value={reportParameters.end_date}
                onChange={(newValue) =>
                  setReportParameters({
                    ...reportParameters,
                    end_date: newValue || dayjs(),
                  })
                }
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>الفرع</InputLabel>
                <Select
                  value={reportParameters.branch}
                  onChange={(e) =>
                    setReportParameters({
                      ...reportParameters,
                      branch: e.target.value,
                    })
                  }
                  label="الفرع"
                >
                  <MenuItem value="">جميع الفروع</MenuItem>
                  <MenuItem value="1">الفرع الرئيسي</MenuItem>
                  <MenuItem value="2">فرع الملز</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {selectedReportType === 'inventory' && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>الفئة</InputLabel>
                  <Select
                    value={reportParameters.category}
                    onChange={(e) =>
                      setReportParameters({
                        ...reportParameters,
                        category: e.target.value,
                      })
                    }
                    label="الفئة"
                  >
                    <MenuItem value="">جميع الفئات</MenuItem>
                    <MenuItem value="1">مسكنات الألم</MenuItem>
                    <MenuItem value="2">المضادات الحيوية</MenuItem>
                    <MenuItem value="3">أدوية القلب</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          <Button
            variant="contained"
            onClick={handleGenerateReport}
            disabled={loading}
            startIcon={<Assessment />}
          >
            إنشاء التقرير
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
