# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-13 23:05+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Yetkilendirme header'i boşlukla sınırlandırılmış iki değer bulundurmak "
"zorunda"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Verilen token hiçbir token tipi için geçerli değil"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token tanınabilir bir kullanıcı kimliği içermiyor"

#: authentication.py:132
msgid "User not found"
msgstr "Kullanıcı bulunamadı"

#: authentication.py:135
msgid "User is inactive"
msgstr "Kullanıcı etkin değil"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Tanınmayan algortima tipi '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "{} kullanmak için cryptography yüklemeniz gerekiyor."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token geçersiz veya süresi geçmiş"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "Geçersiz algoritma belirtildi"

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token geçersiz veya süresi geçmiş"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token geçersiz veya süresi geçmiş"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Verilen kimlik bilgileriyle aktif bir hesap bulunamadı"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Verilen kimlik bilgileriyle aktif bir hesap bulunamadı"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr "'{}' ayarı kaldırıldı. Mevcut ayarlar için '{}' adresini ziyaret edin."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "kullanıcı"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "oluşturulma tarihi"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "sona erme tarihi"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Kara Listesi"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Tipi veya geçerlilik süresi olmayan token oluşturulamaz"

#: tokens.py:126
msgid "Token has no id"
msgstr "Token'in id'si yok"

#: tokens.py:138
msgid "Token has no type"
msgstr "Token'in tipi yok"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Token'in tipi yanlış"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Token'in '{}' claim'i yok"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Token'in '{}' claim'i sona erdi"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Token kara listeye alınmış"
