# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-20 17:23+0100\n"
"PO-Revision-Date: 2021-06-24 13:37+0200\n"
"Last-Translator: Aymeric Augustin <<EMAIL>>\n"
"Language-Team: Slovak (http://www.transifex.com/projects/p/django-debug-"
"toolbar/language/sk/)\n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"
"X-Generator: Poedit 2.4.2\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr "Debug Toolbar"

#: panels/cache.py:180
msgid "Cache"
msgstr "Cache"

#: panels/cache.py:186
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d volanie za %(time).2fms"
msgstr[1] "%(cache_calls)d volania za %(time).2fms"
msgstr[2] "%(cache_calls)d volaní za %(time).2fms"
msgstr[3] "%(cache_calls)d volaní za %(time).2fms"

#: panels/cache.py:195
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Cache volania z %(count)d backendu"
msgstr[1] "Cache volania z %(count)d backendov"
msgstr[2] "Cache volania z %(count)d backendu"
msgstr[3] "Cache volania z %(count)d backendov"

#: panels/headers.py:31
msgid "Headers"
msgstr "Hlavičky"

#: panels/history/panel.py:18 panels/history/panel.py:19
msgid "History"
msgstr ""

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Analýza"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "Zachytiť presmerovania"

#: panels/request.py:16
msgid "Request"
msgstr "Požiadavka"

#: panels/request.py:36
msgid "<no view>"
msgstr "<ziadne zobrazenie>"

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<nedostupny>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Nastavenia"

#: panels/settings.py:20
#, fuzzy, python-format
#| msgid "Settings from <code>%s</code>"
msgid "Settings from %s"
msgstr "Nastavenia z <code>%s</code>"

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d príjemca 1 signálu"
msgstr[1] "%(num_receivers)d príjemcovia 1 signálu"
msgstr[2] "%(num_receivers)d príjemcov 1 signálu"
msgstr[3] "%(num_receivers)d príjemcov 1 signálu"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d príjemca %(num_signals)d signálu"
msgstr[1] "%(num_receivers)d príjemcov %(num_signals)d signálov"
msgstr[2] "%(num_receivers)d príjemcu %(num_signals)d signálu"
msgstr[3] "%(num_receivers)d príjemcov %(num_signals)d signálov"

#: panels/signals.py:67
msgid "Signals"
msgstr "Signály"

#: panels/sql/panel.py:23
msgid "Autocommit"
msgstr "Autocommit"

#: panels/sql/panel.py:24
msgid "Read uncommitted"
msgstr "Read uncommitted"

#: panels/sql/panel.py:25
msgid "Read committed"
msgstr "Read committed"

#: panels/sql/panel.py:26
msgid "Repeatable read"
msgstr "Opakovateľné čítanie"

#: panels/sql/panel.py:27
msgid "Serializable"
msgstr "Premenná"

#: panels/sql/panel.py:39
msgid "Idle"
msgstr "Nečinný"

#: panels/sql/panel.py:40
msgid "Active"
msgstr "Aktívne"

#: panels/sql/panel.py:41
msgid "In transaction"
msgstr "V transakcii"

#: panels/sql/panel.py:42
msgid "In error"
msgstr "Chyba"

#: panels/sql/panel.py:43
msgid "Unknown"
msgstr "(neznámy)"

#: panels/sql/panel.py:130
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:135
#, fuzzy, python-format
#| msgid "%(cache_calls)d call in %(time).2fms"
#| msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] "%(cache_calls)d volanie za %(time).2fms"
msgstr[1] "%(cache_calls)d volania za %(time).2fms"
msgstr[2] "%(cache_calls)d volaní za %(time).2fms"
msgstr[3] "%(cache_calls)d volaní za %(time).2fms"

#: panels/sql/panel.py:147
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: panels/staticfiles.py:84
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Statické súbory (%(num_found)s nájdených, %(num_used)s použitých)"

#: panels/staticfiles.py:105
msgid "Static files"
msgstr "Statické súbory"

#: panels/staticfiles.py:111
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s použitý súbor"
msgstr[1] "%(num_used)s použité súbory"
msgstr[2] "%(num_used)s použitých súborov"
msgstr[3] "%(num_used)s použitých súborov"

#: panels/templates/panel.py:143
msgid "Templates"
msgstr "Šablóny"

#: panels/templates/panel.py:148
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Šablóny (%(num_templates)s spracovaných)"

#: panels/templates/panel.py:180
msgid "No origin"
msgstr ""

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr "Celkovo: %0.2fms"

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Čas"

#: panels/timer.py:44
msgid "User CPU time"
msgstr "Užívateľský čas CPU"

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f msek"

#: panels/timer.py:45
msgid "System CPU time"
msgstr "Systémový čas CPU"

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f msek"

#: panels/timer.py:46
msgid "Total CPU time"
msgstr "Celkový čas CPU"

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f msek"

#: panels/timer.py:47
msgid "Elapsed time"
msgstr "Uplynutý čas"

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f msek"

#: panels/timer.py:49
msgid "Context switches"
msgstr "Prepnutí kontextu"

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d dobrovoľných, %(ivcsw)d nedobrovoľných"

#: panels/versions.py:19
msgid "Versions"
msgstr "Verzie"

#: templates/debug_toolbar/base.html:22
msgid "Hide toolbar"
msgstr "Skryť panel nástrojov"

#: templates/debug_toolbar/base.html:22
msgid "Hide"
msgstr "Skryť"

#: templates/debug_toolbar/base.html:29
msgid "Show toolbar"
msgstr "Zobraziť panel nástrojov"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Zakázať pre ďalšie a nasledujúce požiadavky"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Povoliť pre ďalšie a nasledujúce požiadavky"

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Zhrnutie"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Celkovo volaní"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Celkový čas"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Volaní cache"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Vynechania cache"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Príkazy"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Volania"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Čas (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Typ"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Argumenty"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Kľúčové argumenty"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Backend"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Hlavičky požiadavky"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Kľúč"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Hodnota"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Hlavičky odpovede"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "WSGI prostredie"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""
"Keďže WSGI prostredie dedí z prostredia servera, je nižšie zobrazená iba "
"významná podmnožina."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr ""

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Cesta"

#: templates/debug_toolbar/panels/history.html:12
#, fuzzy
#| msgid "Request headers"
msgid "Request Variables"
msgstr "Hlavičky požiadavky"

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr ""

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Akcia"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Premenná"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Volanie"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "CumTime"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "Za"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "TotTime"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Počet"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Zobraziť informácie"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "View funkcia"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "URL meno"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Cookies"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Žiadne cookies"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Dáta relácie"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Žiadne dáta relácie"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "GET dáta"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Žiadne GET dáta"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "POST dáta"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Žiadne POST dáta"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Nastavenie"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Signál"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Príjemcovia"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s dopyt"
msgstr[1] "%(num)s dopyty"
msgstr[2] "%(num)s dopytu"
msgstr[3] "%(num)s dopytov"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Dopyt"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Časová os"

#: templates/debug_toolbar/panels/sql.html:52
#, fuzzy, python-format
#| msgid "%(count)s message"
#| msgid_plural "%(count)s messages"
msgid "%(count)s similar queries."
msgstr "%(count)s správa"

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Pripojenie:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Úroveň izolácie:"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "Stav transakcie:"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(neznámy)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr "V priebehu tejto požiadavky neboli zaznamenané žiadne SQL dopyty."

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "SQL vysvetlené"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "Vykonané SQL"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Databáza"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "SQL profilované"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Chyba"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "SQL označené"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Prázdny rad"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Cesta k statickému súboru"
msgstr[1] "Cesty k statickým súborom"
msgstr[2] "Cesty k statickým súborom"
msgstr[3] "Ciest k statickým súborom"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(prefix %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Žiadny"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Aplikácia pre statické súbory"
msgstr[1] "Aplikácie pre statické súbory"
msgstr[2] "Aplikácie pre statické súbory"
msgstr[3] "Aplikácií pre statické súbory"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Statický súbor"
msgstr[1] "Statické súbory"
msgstr[2] "Statického súbora"
msgstr[3] "Statických súborov"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s súbor"
msgstr[1] "%(payload_count)s súbory"
msgstr[2] "%(payload_count)s súbora"
msgstr[3] "%(payload_count)s súborov"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Poloha"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Zdrojový kód šablóny:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Cesta k šablóne"
msgstr[1] "Cesty k šablóne"
msgstr[2] "Cesty k šablóne"
msgstr[3] "Ciest k šablóne"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Šablóna"
msgstr[1] "Šablóny"
msgstr[2] "Šablóny"
msgstr[3] "Šablón"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Prepnúť kontext"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "Spracovateľ kontextu"
msgstr[1] "Spracovatelia kontextu"
msgstr[2] "Spracovateľa kontextu"
msgstr[3] "Spracovateľov kontextu"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Využitie prostriedkov"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Prostriedok"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Čas prehliadača"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Časový atribút"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Milisekúnd od spustenia navigácie (+dĺžka)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Meno"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Verzia"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Poloha:"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""
"Django Debug Toolbar zachytil presmerovanie na vyššie uvedenú URL pre účely "
"ladenia. Pre normálne presmerovanie môžete kliknúť na vyššie uvedený odkaz."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
"Dáta pre tento panel už nie sú k dispozícii. Načítajte si prosím stránku a "
"skúste to znova."
