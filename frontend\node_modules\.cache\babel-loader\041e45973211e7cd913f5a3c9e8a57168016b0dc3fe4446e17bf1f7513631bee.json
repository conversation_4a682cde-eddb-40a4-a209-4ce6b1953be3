{"ast": null, "code": "import * as React from 'react';\n/**\n * Generate the props to pass to the container element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the container element of each section of the field.\n */\nexport function useFieldSectionContainerProps(parameters) {\n  const {\n    stateResponse: {\n      // Methods to update the states\n      setSelectedSections\n    },\n    internalPropsWithDefaults: {\n      disabled = false\n    }\n  } = parameters;\n  const createHandleClick = React.useCallback(sectionIndex => event => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call to this function is actually intended, or a side effect.\n    if (disabled || event.isDefaultPrevented()) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback(sectionIndex => ({\n    'data-sectionindex': sectionIndex,\n    onClick: createHandleClick(sectionIndex)\n  }), [createHandleClick]);\n}", "map": {"version": 3, "names": ["React", "useFieldSectionContainerProps", "parameters", "stateResponse", "setSelectedSections", "internalPropsWithDefaults", "disabled", "createHandleClick", "useCallback", "sectionIndex", "event", "isDefaultPrevented", "onClick"], "sources": ["D:/pos/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldSectionContainerProps.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * Generate the props to pass to the container element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the container element of each section of the field.\n */\nexport function useFieldSectionContainerProps(parameters) {\n  const {\n    stateResponse: {\n      // Methods to update the states\n      setSelectedSections\n    },\n    internalPropsWithDefaults: {\n      disabled = false\n    }\n  } = parameters;\n  const createHandleClick = React.useCallback(sectionIndex => event => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call to this function is actually intended, or a side effect.\n    if (disabled || event.isDefaultPrevented()) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback(sectionIndex => ({\n    'data-sectionindex': sectionIndex,\n    onClick: createHandleClick(sectionIndex)\n  }), [createHandleClick]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,6BAA6BA,CAACC,UAAU,EAAE;EACxD,MAAM;IACJC,aAAa,EAAE;MACb;MACAC;IACF,CAAC;IACDC,yBAAyB,EAAE;MACzBC,QAAQ,GAAG;IACb;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,iBAAiB,GAAGP,KAAK,CAACQ,WAAW,CAACC,YAAY,IAAIC,KAAK,IAAI;IACnE;IACA;IACA,IAAIJ,QAAQ,IAAII,KAAK,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC1C;IACF;IACAP,mBAAmB,CAACK,YAAY,CAAC;EACnC,CAAC,EAAE,CAACH,QAAQ,EAAEF,mBAAmB,CAAC,CAAC;EACnC,OAAOJ,KAAK,CAACQ,WAAW,CAACC,YAAY,KAAK;IACxC,mBAAmB,EAAEA,YAAY;IACjCG,OAAO,EAAEL,iBAAiB,CAACE,YAAY;EACzC,CAAC,CAAC,EAAE,CAACF,iBAAiB,CAAC,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}