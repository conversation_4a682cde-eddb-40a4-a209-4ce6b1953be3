from django.contrib import admin
from .models import Category, Manufacturer, Medicine, Batch, StockMovement, StockAlert


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'parent', 'is_active', 'created_at')
    list_filter = ('is_active', 'parent')
    search_fields = ('name', 'code', 'description')
    ordering = ('name',)


@admin.register(Manufacturer)
class ManufacturerAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'country', 'is_active', 'created_at')
    list_filter = ('country', 'is_active')
    search_fields = ('name', 'code', 'contact_info')
    ordering = ('name',)


class BatchInline(admin.TabularInline):
    model = Batch
    extra = 0
    readonly_fields = ('current_quantity',)


@admin.register(Medicine)
class MedicineAdmin(admin.ModelAdmin):
    list_display = ('name', 'generic_name', 'category', 'manufacturer', 'selling_price', 'current_stock', 'is_active')
    list_filter = ('category', 'manufacturer', 'unit', 'requires_prescription', 'is_controlled', 'is_active')
    search_fields = ('name', 'generic_name', 'barcode')
    ordering = ('name',)
    inlines = [BatchInline]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'generic_name', 'barcode', 'category', 'manufacturer')
        }),
        ('التفاصيل', {
            'fields': ('unit', 'strength', 'description', 'storage_conditions')
        }),
        ('الأسعار', {
            'fields': ('cost_price', 'selling_price')
        }),
        ('إدارة المخزون', {
            'fields': ('minimum_stock', 'maximum_stock')
        }),
        ('الخصائص الطبية', {
            'fields': ('requires_prescription', 'is_controlled')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


@admin.register(Batch)
class BatchAdmin(admin.ModelAdmin):
    list_display = ('medicine', 'batch_number', 'branch', 'expiry_date', 'current_quantity', 'is_expired', 'is_active')
    list_filter = ('branch', 'is_active', 'expiry_date', 'medicine__category')
    search_fields = ('medicine__name', 'batch_number')
    ordering = ('expiry_date',)
    readonly_fields = ('is_expired', 'days_to_expiry')

    fieldsets = (
        ('معلومات الدفعة', {
            'fields': ('medicine', 'branch', 'batch_number')
        }),
        ('التواريخ', {
            'fields': ('manufacturing_date', 'expiry_date', 'is_expired', 'days_to_expiry')
        }),
        ('الكميات', {
            'fields': ('initial_quantity', 'current_quantity')
        }),
        ('الأسعار', {
            'fields': ('cost_price', 'selling_price')
        }),
        ('المورد', {
            'fields': ('supplier', 'purchase_order')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ('batch', 'movement_type', 'quantity', 'quantity_change', 'total_amount', 'user', 'created_at')
    list_filter = ('movement_type', 'created_at', 'batch__branch')
    search_fields = ('batch__medicine__name', 'reference_number', 'notes')
    ordering = ('-created_at',)
    readonly_fields = ('total_amount',)


@admin.register(StockAlert)
class StockAlertAdmin(admin.ModelAdmin):
    list_display = ('medicine', 'branch', 'alert_type', 'current_stock', 'threshold', 'is_acknowledged', 'created_at')
    list_filter = ('alert_type', 'is_acknowledged', 'branch', 'created_at')
    search_fields = ('medicine__name', 'message')
    ordering = ('-created_at',)

    actions = ['mark_acknowledged']

    def mark_acknowledged(self, request, queryset):
        queryset.update(is_acknowledged=True, acknowledged_by=request.user)
        self.message_user(request, f"تم الاطلاع على {queryset.count()} تنبيه.")
    mark_acknowledged.short_description = "تم الاطلاع على التنبيهات المحددة"
