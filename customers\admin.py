from django.contrib import admin
from .models import Customer, Doctor, Prescription, PrescriptionItem, LoyaltyProgram, LoyaltyTransaction


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'email', 'loyalty_points', 'total_purchases', 'is_active')
    list_filter = ('gender', 'is_active', 'insurance_company', 'created_at')
    search_fields = ('first_name', 'last_name', 'phone_number', 'email', 'insurance_number')
    ordering = ('first_name', 'last_name')

    fieldsets = (
        ('معلومات شخصية', {
            'fields': ('first_name', 'last_name', 'phone_number', 'email', 'date_of_birth', 'gender', 'address')
        }),
        ('معلومات طبية', {
            'fields': ('allergies', 'chronic_conditions', 'emergency_contact', 'emergency_phone')
        }),
        ('معلومات التأمين', {
            'fields': ('insurance_company', 'insurance_number', 'insurance_expiry')
        }),
        ('برنامج الولاء', {
            'fields': ('loyalty_points', 'total_purchases')
        }),
        ('الحالة والملاحظات', {
            'fields': ('is_active', 'notes')
        }),
    )


@admin.register(Doctor)
class DoctorAdmin(admin.ModelAdmin):
    list_display = ('name', 'specialization', 'license_number', 'phone_number', 'clinic_name', 'is_active')
    list_filter = ('specialization', 'is_active', 'created_at')
    search_fields = ('name', 'license_number', 'clinic_name', 'phone_number')
    ordering = ('name',)

    fieldsets = (
        ('معلومات الطبيب', {
            'fields': ('name', 'specialization', 'license_number')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone_number', 'email')
        }),
        ('معلومات العيادة', {
            'fields': ('clinic_name', 'clinic_address')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


class PrescriptionItemInline(admin.TabularInline):
    model = PrescriptionItem
    extra = 0
    readonly_fields = ('total_price', 'is_fully_dispensed', 'remaining_quantity')


@admin.register(Prescription)
class PrescriptionAdmin(admin.ModelAdmin):
    list_display = ('prescription_number', 'customer', 'doctor', 'prescription_date', 'status', 'total_amount', 'dispensed_by')
    list_filter = ('status', 'branch', 'prescription_date', 'created_at')
    search_fields = ('prescription_number', 'customer__first_name', 'customer__last_name', 'doctor__name')
    ordering = ('-created_at',)
    readonly_fields = ('prescription_number',)
    inlines = [PrescriptionItemInline]

    fieldsets = (
        ('معلومات الوصفة', {
            'fields': ('prescription_number', 'customer', 'doctor', 'branch', 'prescription_date')
        }),
        ('التشخيص والملاحظات', {
            'fields': ('diagnosis', 'notes')
        }),
        ('صورة الوصفة', {
            'fields': ('prescription_image',)
        }),
        ('الحالة والصرف', {
            'fields': ('status', 'total_amount', 'dispensed_by', 'dispensed_at')
        }),
    )


@admin.register(PrescriptionItem)
class PrescriptionItemAdmin(admin.ModelAdmin):
    list_display = ('prescription', 'medicine', 'quantity_prescribed', 'quantity_dispensed', 'is_dispensed', 'total_price')
    list_filter = ('is_dispensed', 'prescription__status')
    search_fields = ('prescription__prescription_number', 'medicine__name')
    ordering = ('-prescription__created_at',)
    readonly_fields = ('total_price', 'is_fully_dispensed', 'remaining_quantity')

    fieldsets = (
        ('معلومات الدواء', {
            'fields': ('prescription', 'medicine')
        }),
        ('الكميات', {
            'fields': ('quantity_prescribed', 'quantity_dispensed', 'is_fully_dispensed', 'remaining_quantity')
        }),
        ('التعليمات', {
            'fields': ('dosage', 'frequency', 'duration', 'instructions')
        }),
        ('الأسعار', {
            'fields': ('unit_price', 'total_price')
        }),
        ('الحالة', {
            'fields': ('is_dispensed',)
        }),
    )


@admin.register(LoyaltyProgram)
class LoyaltyProgramAdmin(admin.ModelAdmin):
    list_display = ('name', 'points_per_riyal', 'riyal_per_point', 'minimum_purchase', 'minimum_redemption', 'is_active')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name',)

    fieldsets = (
        ('معلومات البرنامج', {
            'fields': ('name', 'is_active')
        }),
        ('إعدادات النقاط', {
            'fields': ('points_per_riyal', 'riyal_per_point', 'minimum_purchase', 'minimum_redemption')
        }),
        ('انتهاء النقاط', {
            'fields': ('expiry_months',)
        }),
    )


@admin.register(LoyaltyTransaction)
class LoyaltyTransactionAdmin(admin.ModelAdmin):
    list_display = ('customer', 'transaction_type', 'points', 'sale', 'description', 'created_at')
    list_filter = ('transaction_type', 'created_at')
    search_fields = ('customer__first_name', 'customer__last_name', 'description')
    ordering = ('-created_at',)

    fieldsets = (
        ('معلومات المعاملة', {
            'fields': ('customer', 'transaction_type', 'points')
        }),
        ('التفاصيل', {
            'fields': ('sale', 'description', 'expiry_date')
        }),
    )
