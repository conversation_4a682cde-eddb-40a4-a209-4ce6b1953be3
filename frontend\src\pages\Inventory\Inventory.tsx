import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Fab,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Search,
  Warning,
  CheckCircle,
  Error,
} from '@mui/icons-material';
import { Medicine, Category, Manufacturer, ApiResponse } from '../../types';
import apiService from '../../services/api';

export default function Inventory() {
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingMedicine, setEditingMedicine] = useState<Medicine | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    generic_name: '',
    barcode: '',
    category: '',
    manufacturer: '',
    unit: '',
    price: '',
    requires_prescription: false,
    description: '',
    side_effects: '',
    storage_conditions: '',
  });

  useEffect(() => {
    loadMedicines();
    loadCategories();
    loadManufacturers();
  }, [page, rowsPerPage, searchTerm]);

  const loadMedicines = async () => {
    try {
      setLoading(true);
      const response = await apiService.getMedicines({
        page: page + 1,
        page_size: rowsPerPage,
        search: searchTerm,
      });
      setMedicines(response.results);
      setTotalCount(response.count);
    } catch (err) {
      setError('فشل في تحميل الأدوية');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      // هذا مؤقت - يجب إضافة API للفئات
      setCategories([]);
    } catch (err) {
      console.error('Failed to load categories:', err);
    }
  };

  const loadManufacturers = async () => {
    try {
      // هذا مؤقت - يجب إضافة API للشركات المصنعة
      setManufacturers([]);
    } catch (err) {
      console.error('Failed to load manufacturers:', err);
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleOpenDialog = (medicine?: Medicine) => {
    if (medicine) {
      setEditingMedicine(medicine);
      setFormData({
        name: medicine.name,
        generic_name: medicine.generic_name,
        barcode: medicine.barcode,
        category: medicine.category?.id.toString() || '',
        manufacturer: medicine.manufacturer?.id.toString() || '',
        unit: medicine.unit,
        price: medicine.price.toString(),
        requires_prescription: medicine.requires_prescription,
        description: medicine.description,
        side_effects: medicine.side_effects,
        storage_conditions: medicine.storage_conditions,
      });
    } else {
      setEditingMedicine(null);
      setFormData({
        name: '',
        generic_name: '',
        barcode: '',
        category: '',
        manufacturer: '',
        unit: '',
        price: '',
        requires_prescription: false,
        description: '',
        side_effects: '',
        storage_conditions: '',
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingMedicine(null);
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSubmit = async () => {
    try {
      const medicineData = {
        ...formData,
        price: parseFloat(formData.price),
        category: parseInt(formData.category) || null,
        manufacturer: parseInt(formData.manufacturer) || null,
      };

      if (editingMedicine) {
        await apiService.updateMedicine(editingMedicine.id, medicineData);
      } else {
        await apiService.createMedicine(medicineData);
      }

      handleCloseDialog();
      loadMedicines();
    } catch (err) {
      setError('فشل في حفظ الدواء');
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الدواء؟')) {
      try {
        await apiService.deleteMedicine(id);
        loadMedicines();
      } catch (err) {
        setError('فشل في حذف الدواء');
      }
    }
  };

  const getStockStatus = (medicine: Medicine) => {
    // هذا مؤقت - يجب إضافة معلومات المخزون
    const randomStock = Math.floor(Math.random() * 100);
    if (randomStock < 10) {
      return { status: 'low', color: 'error', icon: <Warning /> };
    } else if (randomStock < 50) {
      return { status: 'medium', color: 'warning', icon: <Error /> };
    } else {
      return { status: 'good', color: 'success', icon: <CheckCircle /> };
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          إدارة المخزون
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
        >
          إضافة دواء جديد
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="البحث في الأدوية"
            value={searchTerm}
            onChange={handleSearch}
            InputProps={{
              startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
          />
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>اسم الدواء</TableCell>
                <TableCell>الاسم العلمي</TableCell>
                <TableCell>الباركود</TableCell>
                <TableCell>الفئة</TableCell>
                <TableCell>السعر</TableCell>
                <TableCell>حالة المخزون</TableCell>
                <TableCell>وصفة طبية</TableCell>
                <TableCell align="center">الإجراءات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {medicines.map((medicine) => {
                const stockStatus = getStockStatus(medicine);
                return (
                  <TableRow key={medicine.id}>
                    <TableCell>{medicine.name}</TableCell>
                    <TableCell>{medicine.generic_name}</TableCell>
                    <TableCell>{medicine.barcode}</TableCell>
                    <TableCell>{medicine.category?.name || '-'}</TableCell>
                    <TableCell>{medicine.price} ر.س</TableCell>
                    <TableCell>
                      <Chip
                        icon={stockStatus.icon}
                        label={stockStatus.status === 'low' ? 'منخفض' : 
                               stockStatus.status === 'medium' ? 'متوسط' : 'جيد'}
                        color={stockStatus.color as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={medicine.requires_prescription ? 'مطلوبة' : 'غير مطلوبة'}
                        color={medicine.requires_prescription ? 'warning' : 'success'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(medicine)}
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDelete(medicine.id)}
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="عدد الصفوف في الصفحة:"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
          }
        />
      </Paper>

      {/* نافذة إضافة/تعديل الدواء */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingMedicine ? 'تعديل الدواء' : 'إضافة دواء جديد'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="اسم الدواء"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="الاسم العلمي"
                value={formData.generic_name}
                onChange={(e) => handleFormChange('generic_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="الباركود"
                value={formData.barcode}
                onChange={(e) => handleFormChange('barcode', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="الوحدة"
                value={formData.unit}
                onChange={(e) => handleFormChange('unit', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="السعر"
                type="number"
                value={formData.price}
                onChange={(e) => handleFormChange('price', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="الوصف"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          <Button variant="contained" onClick={handleSubmit}>
            {editingMedicine ? 'تحديث' : 'إضافة'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
