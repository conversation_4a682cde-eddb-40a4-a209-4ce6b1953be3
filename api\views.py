from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta

from inventory.models import Medicine, Category, Manufacturer, Batch
from customers.models import Customer, Doctor, Prescription
from suppliers.models import Supplier, PurchaseOrder
from pos.models import Sale, SaleItem
from reports.models import Report

from .serializers import (
    MedicineSerializer,
    CustomerSerializer,
    SupplierSerializer,
    SaleSerializer,
    PurchaseOrderSerializer,
    ReportSerializer,
    DashboardStatsSerializer,
)

class MedicineViewSet(viewsets.ModelViewSet):
    queryset = Medicine.objects.all()
    serializer_class = MedicineSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'manufacturer', 'requires_prescription', 'is_active']
    search_fields = ['name', 'generic_name', 'barcode']
    ordering_fields = ['name', 'price', 'created_at']
    ordering = ['name']

class CustomerViewSet(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['gender', 'is_active']
    search_fields = ['name', 'phone', 'email']
    ordering_fields = ['name', 'created_at', 'loyalty_points']
    ordering = ['name']

class SupplierViewSet(viewsets.ModelViewSet):
    queryset = Supplier.objects.all()
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'contact_person', 'phone', 'email']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

class SaleViewSet(viewsets.ModelViewSet):
    queryset = Sale.objects.all()
    serializer_class = SaleSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'payment_method', 'branch']
    search_fields = ['sale_number', 'customer__name']
    ordering_fields = ['sale_date', 'total']
    ordering = ['-sale_date']

    def get_queryset(self):
        queryset = super().get_queryset()
        # فلترة حسب الفرع إذا لم يكن المستخدم مدير نظام
        if not self.request.user.is_superuser and hasattr(self.request.user, 'branch'):
            queryset = queryset.filter(branch=self.request.user.branch)
        return queryset

class PurchaseOrderViewSet(viewsets.ModelViewSet):
    queryset = PurchaseOrder.objects.all()
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'supplier', 'branch']
    search_fields = ['order_number', 'supplier__name']
    ordering_fields = ['order_date', 'total']
    ordering = ['-order_date']

class ReportViewSet(viewsets.ModelViewSet):
    queryset = Report.objects.all()
    serializer_class = ReportSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type']
    search_fields = ['name', 'description']
    ordering_fields = ['generated_at']
    ordering = ['-generated_at']

    @action(detail=False, methods=['post'])
    def generate(self, request):
        """إنشاء تقرير جديد"""
        report_type = request.data.get('type')
        parameters = request.data.get('parameters', {})
        
        # إنشاء التقرير
        report = Report.objects.create(
            name=f"تقرير {report_type} - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
            description=f"تقرير {report_type} تم إنشاؤه تلقائياً",
            type=report_type,
            parameters=parameters,
            generated_by=request.user
        )
        
        # هنا يمكن إضافة منطق إنشاء التقرير الفعلي
        # مثل إنشاء ملف PDF أو Excel
        
        serializer = self.get_serializer(report)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """تحميل التقرير"""
        report = self.get_object()
        
        # هنا يمكن إضافة منطق تحميل الملف
        # مؤقتاً نرجع رسالة
        return Response({
            'message': 'تحميل التقرير غير متاح حالياً',
            'report_id': report.id
        })

class DashboardStatsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """إحصائيات لوحة التحكم"""
        today = timezone.now().date()
        month_start = today.replace(day=1)
        
        # مبيعات اليوم
        today_sales = Sale.objects.filter(
            sale_date__date=today,
            status='completed'
        ).aggregate(total=Sum('total'))['total'] or 0
        
        # إجمالي العملاء
        total_customers = Customer.objects.filter(is_active=True).count()
        
        # أصناف منخفضة المخزون (مؤقت)
        low_stock_items = 5
        
        # أصناف منتهية الصلاحية (مؤقت)
        expired_items = 2
        
        # الوصفات المعلقة
        pending_prescriptions = Prescription.objects.filter(status='pending').count()
        
        # إيرادات الشهر
        monthly_revenue = Sale.objects.filter(
            sale_date__date__gte=month_start,
            status='completed'
        ).aggregate(total=Sum('total'))['total'] or 0
        
        # الأدوية الأكثر مبيعاً (مؤقت)
        top_medicines = Medicine.objects.filter(is_active=True)[:5]
        
        # المبيعات الأخيرة
        recent_sales = Sale.objects.filter(status='completed').order_by('-sale_date')[:5]
        
        data = {
            'total_sales_today': today_sales,
            'total_customers': total_customers,
            'low_stock_items': low_stock_items,
            'expired_items': expired_items,
            'pending_prescriptions': pending_prescriptions,
            'monthly_revenue': monthly_revenue,
            'top_selling_medicines': MedicineSerializer(top_medicines, many=True).data,
            'recent_sales': SaleSerializer(recent_sales, many=True).data,
        }
        
        return Response(data)

class SearchView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """البحث العام في النظام"""
        query = request.GET.get('q', '')
        
        if not query:
            return Response({'results': []})
        
        results = {
            'medicines': [],
            'customers': [],
            'suppliers': [],
        }
        
        # البحث في الأدوية
        medicines = Medicine.objects.filter(
            Q(name__icontains=query) |
            Q(generic_name__icontains=query) |
            Q(barcode__icontains=query)
        )[:5]
        results['medicines'] = MedicineSerializer(medicines, many=True).data
        
        # البحث في العملاء
        customers = Customer.objects.filter(
            Q(name__icontains=query) |
            Q(phone__icontains=query) |
            Q(email__icontains=query)
        )[:5]
        results['customers'] = CustomerSerializer(customers, many=True).data
        
        # البحث في الموردين
        suppliers = Supplier.objects.filter(
            Q(name__icontains=query) |
            Q(contact_person__icontains=query) |
            Q(phone__icontains=query)
        )[:5]
        results['suppliers'] = SupplierSerializer(suppliers, many=True).data
        
        return Response(results)

class LogoutView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """تسجيل الخروج"""
        try:
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            return Response({'message': 'تم تسجيل الخروج بنجاح'})
        except Exception as e:
            return Response({'error': 'حدث خطأ في تسجيل الخروج'}, 
                          status=status.HTTP_400_BAD_REQUEST)
