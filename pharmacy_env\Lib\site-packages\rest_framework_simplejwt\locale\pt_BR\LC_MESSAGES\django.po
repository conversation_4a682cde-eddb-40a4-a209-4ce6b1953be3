# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Cabeçalho de autorização deve conter dois valores delimitados por espaço"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "O token informado não é válido para qualquer tipo de token"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "O token não continha nenhuma identificação reconhecível do usuário"

#: authentication.py:132
msgid "User not found"
msgstr "Usuário não encontrado"

#: authentication.py:135
msgid "User is inactive"
msgstr "Usuário está inativo"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Tipo de algoritmo '{}' não reconhecido"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Você deve ter criptografia instalada para usar {}."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "O token é inválido ou expirado"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "Algoritmo inválido especificado"

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "O token é inválido ou expirado"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "O token é inválido ou expirado"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Usuário e/ou senha incorreto(s)"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Usuário e/ou senha incorreto(s)"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"A configuração '{}' foi removida. Por favor, consulte '{}' para disponível "
"definições."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "usuário"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "criado em"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "expira em"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Lista negra de Tokens"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Não é possível criar token sem tipo ou tempo de vida"

#: tokens.py:126
msgid "Token has no id"
msgstr "Token não tem id"

#: tokens.py:138
msgid "Token has no type"
msgstr "Token não tem nenhum tipo"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Token tem tipo errado"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Token não tem '{}' privilégio"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "O privilégio '{}' do token expirou"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Token está na blacklist"
