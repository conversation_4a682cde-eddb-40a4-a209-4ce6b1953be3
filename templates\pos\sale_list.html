{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة المبيعات - نظام إدارة الصيدليات{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">نقاط البيع</li>
<li class="breadcrumb-item active">قائمة المبيعات</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cash-register me-2"></i>
        قائمة المبيعات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportSales()">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printReport()">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
        <a href="{% url 'pos:new_sale' %}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i>
            بيع جديد
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.total_sales }}</div>
                    <div class="stats-label">إجمالي المبيعات</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-receipt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.total_amount|floatformat:2 }}</div>
                    <div class="stats-label">إجمالي المبلغ (ر.س)</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.completed_sales }}</div>
                    <div class="stats-label">مبيعات مكتملة</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.pending_sales }}</div>
                    <div class="stats-label">مبيعات معلقة</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            فلترة النتائج
        </h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="رقم الفاتورة أو اسم العميل" value="{{ request.GET.search }}">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Sales Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            المبيعات
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>الكاشير</th>
                        <th>المبلغ الإجمالي</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in page_obj %}
                    <tr>
                        <td>
                            <strong>{{ sale.sale_number }}</strong>
                        </td>
                        <td>
                            {{ sale.created_at|date:"Y/m/d H:i" }}
                        </td>
                        <td>
                            {% if sale.customer %}
                                {{ sale.customer.get_full_name }}
                            {% else %}
                                <span class="text-muted">عميل نقدي</span>
                            {% endif %}
                        </td>
                        <td>{{ sale.cashier.get_full_name|default:sale.cashier.username }}</td>
                        <td>
                            <strong>{{ sale.total_amount }} ر.س</strong>
                        </td>
                        <td>
                            {% if sale.payment_method == 'cash' %}
                                <span class="badge bg-success">نقدي</span>
                            {% elif sale.payment_method == 'card' %}
                                <span class="badge bg-info">بطاقة</span>
                            {% elif sale.payment_method == 'mixed' %}
                                <span class="badge bg-warning">مختلط</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if sale.status == 'completed' %}
                                <span class="badge bg-success">مكتمل</span>
                            {% elif sale.status == 'pending' %}
                                <span class="badge bg-warning">معلق</span>
                            {% elif sale.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'pos:sale_detail' sale.id %}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'pos:receipt' sale.id %}" 
                                   class="btn btn-outline-info" title="عرض الإيصال">
                                    <i class="fas fa-receipt"></i>
                                </a>
                                <a href="{% url 'pos:print_receipt' sale.id %}" 
                                   class="btn btn-outline-secondary" title="طباعة" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                                {% if sale.status == 'completed' %}
                                <a href="{% url 'pos:create_return' sale.id %}" 
                                   class="btn btn-outline-warning" title="إرجاع">
                                    <i class="fas fa-undo"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="تصفح المبيعات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مبيعات</h5>
            <p class="text-muted">لم يتم العثور على أي مبيعات تطابق معايير البحث</p>
            <a href="{% url 'pos:new_sale' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء بيع جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportSales() {
    // تصدير المبيعات
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function printReport() {
    // طباعة التقرير
    window.print();
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    if (!document.hidden) {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
