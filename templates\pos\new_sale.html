{% extends 'base.html' %}
{% load static %}

{% block title %}بيع جديد - نظام إدارة الصيدليات{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'pos:sale_list' %}">نقاط البيع</a></li>
<li class="breadcrumb-item active">بيع جديد</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cash-register me-2"></i>
        بيع جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSale()">
                <i class="fas fa-trash me-1"></i>
                مسح الكل
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="saveDraft()">
                <i class="fas fa-save me-1"></i>
                حفظ مسودة
            </button>
        </div>
        <a href="{% url 'pos:sale_list' %}" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Product Search and Selection -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث عن الأدوية
                </h5>
            </div>
            <div class="card-body">
                <div class="search-box mb-3">
                    <input type="text" class="form-control" id="medicineSearch" 
                           placeholder="ابحث عن الدواء بالاسم أو الباركود...">
                    <i class="fas fa-search search-icon"></i>
                </div>
                
                <div id="searchResults" class="d-none">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الدواء</th>
                                    <th>الفئة</th>
                                    <th>السعر</th>
                                    <th>المخزون</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody id="searchResultsBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sale Items -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    عناصر البيع
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الخصم</th>
                                <th>الإجمالي</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody id="saleItemsBody">
                            <tr id="noItemsRow">
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                                    لا توجد عناصر في السلة
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Sale Summary and Payment -->
    <div class="col-lg-4">
        <!-- Customer Selection -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    العميل
                </h5>
            </div>
            <div class="card-body">
                <select class="form-select" id="customerSelect">
                    <option value="">عميل نقدي</option>
                    {% for customer in customers %}
                    <option value="{{ customer.id }}">{{ customer.get_full_name }}</option>
                    {% endfor %}
                </select>
                <button type="button" class="btn btn-sm btn-outline-primary mt-2 w-100" 
                        onclick="addNewCustomer()">
                    <i class="fas fa-plus me-1"></i>
                    إضافة عميل جديد
                </button>
            </div>
        </div>

        <!-- Sale Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    ملخص البيع
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-6">المجموع الفرعي:</div>
                    <div class="col-6 text-end">
                        <span id="subtotal">0.00</span> ر.س
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-6">
                        <label for="discountAmount" class="form-label">الخصم:</label>
                    </div>
                    <div class="col-6">
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control" id="discountAmount" 
                                   value="0" min="0" step="0.01">
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-6">الضريبة (15%):</div>
                    <div class="col-6 text-end">
                        <span id="taxAmount">0.00</span> ر.س
                    </div>
                </div>
                
                <hr>
                
                <div class="row mb-3">
                    <div class="col-6"><strong>الإجمالي:</strong></div>
                    <div class="col-6 text-end">
                        <strong><span id="totalAmount">0.00</span> ر.س</strong>
                    </div>
                </div>
                
                <!-- Payment Method -->
                <div class="mb-3">
                    <label class="form-label">طريقة الدفع</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="paymentMethod" 
                               id="cashPayment" value="cash" checked>
                        <label class="btn btn-outline-success" for="cashPayment">
                            <i class="fas fa-money-bill-wave"></i><br>نقدي
                        </label>
                        
                        <input type="radio" class="btn-check" name="paymentMethod" 
                               id="cardPayment" value="card">
                        <label class="btn btn-outline-info" for="cardPayment">
                            <i class="fas fa-credit-card"></i><br>بطاقة
                        </label>
                        
                        <input type="radio" class="btn-check" name="paymentMethod" 
                               id="mixedPayment" value="mixed">
                        <label class="btn btn-outline-warning" for="mixedPayment">
                            <i class="fas fa-coins"></i><br>مختلط
                        </label>
                    </div>
                </div>
                
                <!-- Amount Paid -->
                <div class="mb-3">
                    <label for="amountPaid" class="form-label">المبلغ المدفوع</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="amountPaid" 
                               step="0.01" min="0">
                        <span class="input-group-text">ر.س</span>
                    </div>
                </div>
                
                <!-- Change -->
                <div class="row mb-3">
                    <div class="col-6">الباقي:</div>
                    <div class="col-6 text-end">
                        <span id="changeAmount" class="text-success">0.00</span> ر.س
                    </div>
                </div>
                
                <!-- Notes -->
                <div class="mb-3">
                    <label for="saleNotes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="saleNotes" rows="2" 
                              placeholder="ملاحظات إضافية..."></textarea>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary btn-lg" 
                            id="completeSaleBtn" onclick="completeSale()">
                        <i class="fas fa-check me-2"></i>
                        إتمام البيع
                    </button>
                    <button type="button" class="btn btn-outline-secondary" 
                            onclick="holdSale()">
                        <i class="fas fa-pause me-2"></i>
                        تعليق البيع
                    </button>
                </div>
            </div>
        </div>

        <!-- Cash Register Info -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cash-register me-2"></i>
                    صندوق النقد
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-6">الكاشير:</div>
                    <div class="col-6 text-end">{{ user.get_full_name|default:user.username }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6">الرصيد الحالي:</div>
                    <div class="col-6 text-end">{{ cash_register.current_amount }} ر.س</div>
                </div>
                <div class="row">
                    <div class="col-6">وقت الفتح:</div>
                    <div class="col-6 text-end">{{ cash_register.opened_at|date:"H:i" }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Customer Modal -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="firstName" class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" id="firstName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="lastName" class="form-label">الاسم الأخير</label>
                            <input type="text" class="form-control" id="lastName" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phone" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.btn-check:checked + .btn-outline-success {
    background-color: #198754;
    border-color: #198754;
    color: white;
}

.btn-check:checked + .btn-outline-info {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: white;
}

.btn-check:checked + .btn-outline-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: black;
}

.sale-item-row {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.quantity-input {
    width: 80px;
}

.discount-input {
    width: 100px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let saleItems = [];
let searchTimeout;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // ربط أحداث البحث
    document.getElementById('medicineSearch').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(searchMedicines, 300);
    });
    
    // ربط أحداث الحساب
    document.getElementById('discountAmount').addEventListener('input', calculateTotal);
    document.getElementById('amountPaid').addEventListener('input', calculateChange);
    
    // تحديث المجموع عند تغيير طريقة الدفع
    document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => {
        radio.addEventListener('change', calculateTotal);
    });
});

// البحث عن الأدوية
function searchMedicines() {
    const query = document.getElementById('medicineSearch').value.trim();
    
    if (query.length < 2) {
        document.getElementById('searchResults').classList.add('d-none');
        return;
    }
    
    fetch(`/pos/api/search-medicine/?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.medicines);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

// عرض نتائج البحث
function displaySearchResults(medicines) {
    const tbody = document.getElementById('searchResultsBody');
    const resultsDiv = document.getElementById('searchResults');
    
    if (medicines.length === 0) {
        resultsDiv.classList.add('d-none');
        return;
    }
    
    tbody.innerHTML = '';
    
    medicines.forEach(medicine => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${medicine.name}</strong><br>
                <small class="text-muted">${medicine.generic_name}</small>
            </td>
            <td>${medicine.category}</td>
            <td>${medicine.selling_price} ر.س</td>
            <td>
                ${medicine.current_stock > 0 ? 
                    `<span class="badge bg-success">${medicine.current_stock}</span>` :
                    `<span class="badge bg-danger">نفد</span>`
                }
            </td>
            <td>
                <button class="btn btn-sm btn-primary" 
                        onclick="addToSale(${medicine.id})"
                        ${medicine.current_stock <= 0 ? 'disabled' : ''}>
                    <i class="fas fa-plus"></i> إضافة
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    resultsDiv.classList.remove('d-none');
}

// إضافة دواء للبيع
function addToSale(medicineId) {
    fetch(`/pos/api/get-medicine/${medicineId}/`)
        .then(response => response.json())
        .then(medicine => {
            // التحقق من وجود الدواء في السلة
            const existingItem = saleItems.find(item => item.medicine_id === medicineId);
            
            if (existingItem) {
                existingItem.quantity += 1;
                existingItem.total_price = existingItem.quantity * existingItem.unit_price;
            } else {
                saleItems.push({
                    medicine_id: medicineId,
                    name: medicine.name,
                    unit_price: parseFloat(medicine.selling_price),
                    quantity: 1,
                    discount_amount: 0,
                    total_price: parseFloat(medicine.selling_price),
                    current_stock: medicine.current_stock
                });
            }
            
            updateSaleItemsDisplay();
            calculateTotal();
            
            // مسح البحث
            document.getElementById('medicineSearch').value = '';
            document.getElementById('searchResults').classList.add('d-none');
        })
        .catch(error => {
            console.error('خطأ في إضافة الدواء:', error);
            alert('حدث خطأ في إضافة الدواء');
        });
}

// تحديث عرض عناصر البيع
function updateSaleItemsDisplay() {
    const tbody = document.getElementById('saleItemsBody');
    const noItemsRow = document.getElementById('noItemsRow');
    
    if (saleItems.length === 0) {
        noItemsRow.style.display = '';
        return;
    }
    
    noItemsRow.style.display = 'none';
    
    // إزالة الصفوف الموجودة (عدا صف "لا توجد عناصر")
    const existingRows = tbody.querySelectorAll('.sale-item-row');
    existingRows.forEach(row => row.remove());
    
    saleItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.className = 'sale-item-row';
        row.innerHTML = `
            <td>
                <strong>${item.name}</strong>
            </td>
            <td>${item.unit_price.toFixed(2)} ر.س</td>
            <td>
                <input type="number" class="form-control quantity-input" 
                       value="${item.quantity}" min="1" max="${item.current_stock}"
                       onchange="updateQuantity(${index}, this.value)">
            </td>
            <td>
                <input type="number" class="form-control discount-input" 
                       value="${item.discount_amount}" min="0" step="0.01"
                       onchange="updateDiscount(${index}, this.value)">
            </td>
            <td><strong>${item.total_price.toFixed(2)} ر.س</strong></td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="removeItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تحديث الكمية
function updateQuantity(index, quantity) {
    const item = saleItems[index];
    const newQuantity = parseInt(quantity);
    
    if (newQuantity > item.current_stock) {
        alert(`الكمية المتاحة: ${item.current_stock}`);
        return;
    }
    
    if (newQuantity <= 0) {
        removeItem(index);
        return;
    }
    
    item.quantity = newQuantity;
    item.total_price = (item.quantity * item.unit_price) - item.discount_amount;
    
    updateSaleItemsDisplay();
    calculateTotal();
}

// تحديث الخصم
function updateDiscount(index, discount) {
    const item = saleItems[index];
    const newDiscount = parseFloat(discount) || 0;
    
    if (newDiscount > (item.quantity * item.unit_price)) {
        alert('الخصم لا يمكن أن يكون أكبر من سعر الصنف');
        return;
    }
    
    item.discount_amount = newDiscount;
    item.total_price = (item.quantity * item.unit_price) - item.discount_amount;
    
    updateSaleItemsDisplay();
    calculateTotal();
}

// حذف عنصر
function removeItem(index) {
    saleItems.splice(index, 1);
    updateSaleItemsDisplay();
    calculateTotal();
}

// حساب الإجمالي
function calculateTotal() {
    const subtotal = saleItems.reduce((sum, item) => sum + item.total_price, 0);
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const taxRate = 0.15; // 15% ضريبة
    
    const subtotalAfterDiscount = subtotal - discountAmount;
    const taxAmount = subtotalAfterDiscount * taxRate;
    const totalAmount = subtotalAfterDiscount + taxAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('taxAmount').textContent = taxAmount.toFixed(2);
    document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);
    
    // تحديث المبلغ المدفوع تلقائياً
    const amountPaidInput = document.getElementById('amountPaid');
    if (!amountPaidInput.value) {
        amountPaidInput.value = totalAmount.toFixed(2);
    }
    
    calculateChange();
}

// حساب الباقي
function calculateChange() {
    const totalAmount = parseFloat(document.getElementById('totalAmount').textContent);
    const amountPaid = parseFloat(document.getElementById('amountPaid').value) || 0;
    const change = amountPaid - totalAmount;
    
    document.getElementById('changeAmount').textContent = Math.max(0, change).toFixed(2);
    
    // تفعيل/تعطيل زر إتمام البيع
    const completeSaleBtn = document.getElementById('completeSaleBtn');
    completeSaleBtn.disabled = amountPaid < totalAmount || saleItems.length === 0;
}

// إتمام البيع
function completeSale() {
    if (saleItems.length === 0) {
        alert('يجب إضافة عناصر للبيع');
        return;
    }
    
    const totalAmount = parseFloat(document.getElementById('totalAmount').textContent);
    const amountPaid = parseFloat(document.getElementById('amountPaid').value) || 0;
    
    if (amountPaid < totalAmount) {
        alert('المبلغ المدفوع أقل من الإجمالي');
        return;
    }
    
    const saleData = {
        customer_id: document.getElementById('customerSelect').value || null,
        items: saleItems,
        subtotal: parseFloat(document.getElementById('subtotal').textContent),
        discount_amount: parseFloat(document.getElementById('discountAmount').value) || 0,
        tax_amount: parseFloat(document.getElementById('taxAmount').textContent),
        total_amount: totalAmount,
        payment_method: document.querySelector('input[name="paymentMethod"]:checked').value,
        amount_paid: amountPaid,
        change_amount: parseFloat(document.getElementById('changeAmount').textContent),
        notes: document.getElementById('saleNotes').value
    };
    
    // تعطيل الزر ومنع النقر المتكرر
    const btn = document.getElementById('completeSaleBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
    
    fetch('/pos/sale/create/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إنشاء البيع بنجاح!');
            // فتح الإيصال في نافذة جديدة
            window.open(`/pos/sale/${data.sale_id}/print/`, '_blank');
            // العودة لقائمة المبيعات
            window.location.href = '/pos/sales/';
        } else {
            alert('خطأ: ' + data.error);
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check me-2"></i>إتمام البيع';
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في إتمام البيع');
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-check me-2"></i>إتمام البيع';
    });
}

// مسح البيع
function clearSale() {
    if (saleItems.length > 0 && !confirm('هل تريد مسح جميع العناصر؟')) {
        return;
    }
    
    saleItems = [];
    updateSaleItemsDisplay();
    calculateTotal();
    
    document.getElementById('customerSelect').value = '';
    document.getElementById('discountAmount').value = '0';
    document.getElementById('amountPaid').value = '';
    document.getElementById('saleNotes').value = '';
    document.getElementById('medicineSearch').value = '';
    document.getElementById('searchResults').classList.add('d-none');
}

// إضافة عميل جديد
function addNewCustomer() {
    const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
    modal.show();
}

// حفظ العميل الجديد
function saveCustomer() {
    // هنا يمكن إضافة كود حفظ العميل
    alert('سيتم إضافة هذه الميزة قريباً');
}

// حفظ مسودة
function saveDraft() {
    if (saleItems.length === 0) {
        alert('لا توجد عناصر لحفظها');
        return;
    }
    
    // حفظ في localStorage
    const draft = {
        items: saleItems,
        customer_id: document.getElementById('customerSelect').value,
        discount_amount: document.getElementById('discountAmount').value,
        notes: document.getElementById('saleNotes').value,
        timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('sale_draft', JSON.stringify(draft));
    alert('تم حفظ المسودة');
}

// تحميل مسودة (يمكن استدعاؤها عند تحميل الصفحة)
function loadDraft() {
    const draft = localStorage.getItem('sale_draft');
    if (draft) {
        const data = JSON.parse(draft);
        // تحميل البيانات...
    }
}
</script>
{% endblock %}
