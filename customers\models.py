from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator, MinValueValidator
from decimal import Decimal


class Customer(models.Model):
    """
    Customer/Patient model
    """
    GENDER_CHOICES = [
        ('M', _('ذكر')),
        ('F', _('أنثى')),
    ]

    # Personal Information
    first_name = models.CharField(
        max_length=50,
        verbose_name=_('الاسم الأول')
    )

    last_name = models.CharField(
        max_length=50,
        verbose_name=_('اسم العائلة')
    )

    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message=_("رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    )

    phone_number = models.Char<PERSON>ield(
        validators=[phone_regex],
        max_length=17,
        unique=True,
        verbose_name=_('رقم الهاتف')
    )

    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name=_('البريد الإلكتروني')
    )

    date_of_birth = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الميلاد')
    )

    gender = models.CharField(
        max_length=1,
        choices=GENDER_CHOICES,
        blank=True,
        verbose_name=_('الجنس')
    )

    address = models.TextField(
        blank=True,
        verbose_name=_('العنوان')
    )

    # Medical Information
    allergies = models.TextField(
        blank=True,
        verbose_name=_('الحساسيات')
    )

    chronic_conditions = models.TextField(
        blank=True,
        verbose_name=_('الأمراض المزمنة')
    )

    emergency_contact = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('جهة الاتصال في الطوارئ')
    )

    emergency_phone = models.CharField(
        validators=[phone_regex],
        max_length=17,
        blank=True,
        verbose_name=_('هاتف الطوارئ')
    )

    # Insurance Information
    insurance_company = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('شركة التأمين')
    )

    insurance_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('رقم التأمين')
    )

    insurance_expiry = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('انتهاء التأمين')
    )

    # Loyalty Program
    loyalty_points = models.IntegerField(
        default=0,
        verbose_name=_('نقاط الولاء')
    )

    total_purchases = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('إجمالي المشتريات')
    )

    # Status
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('عميل')
        verbose_name_plural = _('العملاء')
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        if self.date_of_birth:
            from django.utils import timezone
            today = timezone.now().date()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None


class Doctor(models.Model):
    """
    Doctor/Physician model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الطبيب')
    )

    specialization = models.CharField(
        max_length=100,
        verbose_name=_('التخصص')
    )

    license_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('رقم الترخيص')
    )

    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message=_("رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    )

    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        blank=True,
        verbose_name=_('رقم الهاتف')
    )

    email = models.EmailField(
        blank=True,
        verbose_name=_('البريد الإلكتروني')
    )

    clinic_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('اسم العيادة')
    )

    clinic_address = models.TextField(
        blank=True,
        verbose_name=_('عنوان العيادة')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('طبيب')
        verbose_name_plural = _('الأطباء')
        ordering = ['name']

    def __str__(self):
        return f"د. {self.name} - {self.specialization}"


class Prescription(models.Model):
    """
    Medical prescription model
    """
    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('partial', _('جزئي')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
    ]

    prescription_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم الوصفة')
    )

    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='prescriptions',
        verbose_name=_('المريض')
    )

    doctor = models.ForeignKey(
        Doctor,
        on_delete=models.PROTECT,
        related_name='prescriptions',
        verbose_name=_('الطبيب')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.PROTECT,
        related_name='prescriptions',
        verbose_name=_('الفرع')
    )

    prescription_date = models.DateField(
        verbose_name=_('تاريخ الوصفة')
    )

    diagnosis = models.TextField(
        blank=True,
        verbose_name=_('التشخيص')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    # Prescription image/scan
    prescription_image = models.ImageField(
        upload_to='prescriptions/',
        blank=True,
        null=True,
        verbose_name=_('صورة الوصفة')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المبلغ الإجمالي')
    )

    dispensed_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='dispensed_prescriptions',
        verbose_name=_('تم الصرف بواسطة')
    )

    dispensed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الصرف')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('وصفة طبية')
        verbose_name_plural = _('الوصفات الطبية')
        ordering = ['-created_at']

    def __str__(self):
        return f"وصفة {self.prescription_number} - {self.customer.full_name}"

    def save(self, *args, **kwargs):
        if not self.prescription_number:
            self.prescription_number = self.generate_prescription_number()
        super().save(*args, **kwargs)

    def generate_prescription_number(self):
        """Generate unique prescription number"""
        from django.utils import timezone
        today = timezone.now().strftime('%Y%m%d')
        last_prescription = Prescription.objects.filter(
            prescription_number__startswith=f"RX{today}"
        ).order_by('-prescription_number').first()

        if last_prescription:
            last_number = int(last_prescription.prescription_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"RX{today}{new_number:04d}"


class PrescriptionItem(models.Model):
    """
    Individual medicines in a prescription
    """
    prescription = models.ForeignKey(
        Prescription,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('الوصفة')
    )

    medicine = models.ForeignKey(
        'inventory.Medicine',
        on_delete=models.PROTECT,
        verbose_name=_('الدواء')
    )

    quantity_prescribed = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name=_('الكمية الموصوفة')
    )

    quantity_dispensed = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name=_('الكمية المصروفة')
    )

    dosage = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('الجرعة')
    )

    frequency = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('التكرار')
    )

    duration = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('المدة')
    )

    instructions = models.TextField(
        blank=True,
        verbose_name=_('التعليمات')
    )

    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('سعر الوحدة')
    )

    total_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('السعر الإجمالي')
    )

    is_dispensed = models.BooleanField(
        default=False,
        verbose_name=_('تم الصرف')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('عنصر الوصفة')
        verbose_name_plural = _('عناصر الوصفة')

    def __str__(self):
        return f"{self.medicine.name} - {self.quantity_prescribed}"

    @property
    def is_fully_dispensed(self):
        return self.quantity_dispensed >= self.quantity_prescribed

    @property
    def remaining_quantity(self):
        return max(0, self.quantity_prescribed - self.quantity_dispensed)


class LoyaltyProgram(models.Model):
    """
    Loyalty program configuration
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم البرنامج')
    )

    points_per_riyal = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('1.00'),
        verbose_name=_('نقاط لكل ريال')
    )

    riyal_per_point = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.01'),
        verbose_name=_('ريال لكل نقطة')
    )

    minimum_purchase = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('10.00'),
        verbose_name=_('الحد الأدنى للشراء')
    )

    minimum_redemption = models.IntegerField(
        default=100,
        verbose_name=_('الحد الأدنى للاستبدال')
    )

    expiry_months = models.IntegerField(
        default=12,
        verbose_name=_('انتهاء النقاط (شهور)')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('برنامج الولاء')
        verbose_name_plural = _('برامج الولاء')

    def __str__(self):
        return self.name


class LoyaltyTransaction(models.Model):
    """
    Loyalty points transactions
    """
    TRANSACTION_TYPES = [
        ('earned', _('مكتسب')),
        ('redeemed', _('مستبدل')),
        ('expired', _('منتهي')),
        ('adjusted', _('معدل')),
    ]

    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='loyalty_transactions',
        verbose_name=_('العميل')
    )

    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        verbose_name=_('نوع المعاملة')
    )

    points = models.IntegerField(
        verbose_name=_('النقاط')
    )

    sale = models.ForeignKey(
        'pos.Sale',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('البيع')
    )

    description = models.CharField(
        max_length=200,
        verbose_name=_('الوصف')
    )

    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الانتهاء')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ المعاملة')
    )

    class Meta:
        verbose_name = _('معاملة ولاء')
        verbose_name_plural = _('معاملات الولاء')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.customer.full_name} - {self.points} نقطة"
