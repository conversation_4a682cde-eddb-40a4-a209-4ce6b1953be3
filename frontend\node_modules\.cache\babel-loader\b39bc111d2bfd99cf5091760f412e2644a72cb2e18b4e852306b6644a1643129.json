{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\n/**\n * Utility hook to check if a given value is valid based on the provided validation props.\n * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n * @param {UseValidationOptions<TValue, TError, TValidationProps>} options The options to configure the hook.\n * @param {TValue} options.value The value to validate.\n * @param {PickersTimezone} options.timezone The timezone to use for the validation.\n * @param {Validator<TValue, TError, TValidationProps>} options.validator The validator function to use.\n * @param {TValidationProps} options.props The validation props, they differ depending on the component.\n * @param {(error: TError, value: TValue) => void} options.onError Callback fired when the error associated with the current value changes.\n */\nexport function useValidation(options) {\n  const {\n    props,\n    validator,\n    value,\n    timezone,\n    onError\n  } = options;\n  const adapter = useLocalizationContext();\n  const previousValidationErrorRef = React.useRef(validator.valueManager.defaultErrorState);\n  const validationError = validator({\n    adapter,\n    value,\n    timezone,\n    props\n  });\n  const hasValidationError = validator.valueManager.hasError(validationError);\n  React.useEffect(() => {\n    if (onError && !validator.valueManager.isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n    previousValidationErrorRef.current = validationError;\n  }, [validator, onError, validationError, value]);\n  const getValidationErrorForNewValue = useEventCallback(newValue => {\n    return validator({\n      adapter,\n      value: newValue,\n      timezone,\n      props\n    });\n  });\n  return {\n    validationError,\n    hasValidationError,\n    getValidationErrorForNewValue\n  };\n}", "map": {"version": 3, "names": ["React", "useEventCallback", "useLocalizationContext", "useValidation", "options", "props", "validator", "value", "timezone", "onError", "adapter", "previousValidationErrorRef", "useRef", "valueManager", "defaultErrorState", "validationError", "hasValidationError", "<PERSON><PERSON><PERSON><PERSON>", "useEffect", "isSameError", "current", "getValidationErrorForNewValue", "newValue"], "sources": ["D:/pos/frontend/node_modules/@mui/x-date-pickers/esm/validation/useValidation.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\n/**\n * Utility hook to check if a given value is valid based on the provided validation props.\n * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n * @param {UseValidationOptions<TValue, TError, TValidationProps>} options The options to configure the hook.\n * @param {TValue} options.value The value to validate.\n * @param {PickersTimezone} options.timezone The timezone to use for the validation.\n * @param {Validator<TValue, TError, TValidationProps>} options.validator The validator function to use.\n * @param {TValidationProps} options.props The validation props, they differ depending on the component.\n * @param {(error: TError, value: TValue) => void} options.onError Callback fired when the error associated with the current value changes.\n */\nexport function useValidation(options) {\n  const {\n    props,\n    validator,\n    value,\n    timezone,\n    onError\n  } = options;\n  const adapter = useLocalizationContext();\n  const previousValidationErrorRef = React.useRef(validator.valueManager.defaultErrorState);\n  const validationError = validator({\n    adapter,\n    value,\n    timezone,\n    props\n  });\n  const hasValidationError = validator.valueManager.hasError(validationError);\n  React.useEffect(() => {\n    if (onError && !validator.valueManager.isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n    previousValidationErrorRef.current = validationError;\n  }, [validator, onError, validationError, value]);\n  const getValidationErrorForNewValue = useEventCallback(newValue => {\n    return validator({\n      adapter,\n      value: newValue,\n      timezone,\n      props\n    });\n  });\n  return {\n    validationError,\n    hasValidationError,\n    getValidationErrorForNewValue\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,OAAO,EAAE;EACrC,MAAM;IACJC,KAAK;IACLC,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGL,OAAO;EACX,MAAMM,OAAO,GAAGR,sBAAsB,CAAC,CAAC;EACxC,MAAMS,0BAA0B,GAAGX,KAAK,CAACY,MAAM,CAACN,SAAS,CAACO,YAAY,CAACC,iBAAiB,CAAC;EACzF,MAAMC,eAAe,GAAGT,SAAS,CAAC;IAChCI,OAAO;IACPH,KAAK;IACLC,QAAQ;IACRH;EACF,CAAC,CAAC;EACF,MAAMW,kBAAkB,GAAGV,SAAS,CAACO,YAAY,CAACI,QAAQ,CAACF,eAAe,CAAC;EAC3Ef,KAAK,CAACkB,SAAS,CAAC,MAAM;IACpB,IAAIT,OAAO,IAAI,CAACH,SAAS,CAACO,YAAY,CAACM,WAAW,CAACJ,eAAe,EAAEJ,0BAA0B,CAACS,OAAO,CAAC,EAAE;MACvGX,OAAO,CAACM,eAAe,EAAER,KAAK,CAAC;IACjC;IACAI,0BAA0B,CAACS,OAAO,GAAGL,eAAe;EACtD,CAAC,EAAE,CAACT,SAAS,EAAEG,OAAO,EAAEM,eAAe,EAAER,KAAK,CAAC,CAAC;EAChD,MAAMc,6BAA6B,GAAGpB,gBAAgB,CAACqB,QAAQ,IAAI;IACjE,OAAOhB,SAAS,CAAC;MACfI,OAAO;MACPH,KAAK,EAAEe,QAAQ;MACfd,QAAQ;MACRH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO;IACLU,eAAe;IACfC,kBAAkB;IAClBK;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}