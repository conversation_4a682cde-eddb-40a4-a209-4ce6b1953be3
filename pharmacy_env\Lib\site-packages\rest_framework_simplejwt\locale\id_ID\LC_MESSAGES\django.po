# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2023
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"PO-Revision-Date: 2023-03-09 08:14+0000\n"
"Last-Translator: Kira <<EMAIL>>\n"
"Language: id_ID\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "Header otorisasi harus berisi dua nilai yang dipisahkan spasi"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Token yang diberikan tidak valid untuk semua jenis token"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token tidak mengandung identifikasi pengguna yang dapat dikenali"

#: authentication.py:132
msgid "User not found"
msgstr "Pengguna tidak ditemukan"

#: authentication.py:135
msgid "User is inactive"
msgstr "Pengguna tidak aktif"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Jenis algoritma tidak dikenal '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Anda harus memasang cryptography untuk menggunakan {}."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""
"Tipe '{}' tidak dikenali, 'leeway' harus bertipe int, float, atau timedelta."

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token tidak valid atau kedaluwarsa"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "Algoritma yang ditentukan tidak valid"

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token tidak valid atau kedaluwarsa"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token tidak valid atau kedaluwarsa"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Tidak ada akun aktif yang ditemukan dengan kredensial yang diberikan"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Tidak ada akun aktif yang ditemukan dengan kredensial yang diberikan"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Setelan '{}' telah dihapus. Silakan merujuk ke '{}' untuk pengaturan yang "
"tersedia."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "pengguna"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "created at"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "kedaluwarsa pada"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Daftar Hitam Token"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Tidak dapat membuat token tanpa tipe atau masa pakai"

#: tokens.py:126
msgid "Token has no id"
msgstr "Token tidak memiliki id"

#: tokens.py:138
msgid "Token has no type"
msgstr "Token tidak memiliki tipe"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Jenis token salah"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Token tidak memiliki klaim '{}'"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Klaim token '{}' telah kedaluwarsa"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Token masuk daftar hitam"
