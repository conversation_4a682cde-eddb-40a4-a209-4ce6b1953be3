{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة صندوق النقد - نظام إدارة الصيدليات{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'pos:sale_list' %}">نقاط البيع</a></li>
<li class="breadcrumb-item active">إدارة صندوق النقد</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cash-register me-2"></i>
        إدارة صندوق النقد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if cash_register and cash_register.is_open %}
        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#closeCashRegisterModal">
            <i class="fas fa-lock me-1"></i>
            إغلاق الصندوق
        </button>
        {% else %}
        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#openCashRegisterModal">
            <i class="fas fa-unlock me-1"></i>
            فتح الصندوق
        </button>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Cash Register Status -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    حالة صندوق النقد
                </h5>
            </div>
            <div class="card-body">
                {% if cash_register %}
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if cash_register.is_open %}
                                        <span class="badge bg-success">مفتوح</span>
                                    {% else %}
                                        <span class="badge bg-danger">مغلق</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الكاشير:</strong></td>
                                <td>{{ cash_register.cashier.get_full_name|default:cash_register.cashier.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>الفرع:</strong></td>
                                <td>{{ cash_register.branch.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الفتح:</strong></td>
                                <td>{{ cash_register.opened_at|date:"Y/m/d H:i:s" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الرصيد الافتتاحي:</strong></td>
                                <td>{{ cash_register.opening_amount }} ر.س</td>
                            </tr>
                            <tr>
                                <td><strong>الرصيد الحالي:</strong></td>
                                <td><strong class="text-success">{{ cash_register.current_amount }} ر.س</strong></td>
                            </tr>
                            {% if not cash_register.is_open %}
                            <tr>
                                <td><strong>الرصيد الختامي:</strong></td>
                                <td>{{ cash_register.closing_amount }} ر.س</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإغلاق:</strong></td>
                                <td>{{ cash_register.closed_at|date:"Y/m/d H:i:s" }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if cash_register.notes %}
                <div class="mt-3">
                    <strong>ملاحظات:</strong>
                    <p class="text-muted">{{ cash_register.notes }}</p>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-cash-register fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد صندوق نقد</h5>
                    <p class="text-muted">يجب فتح صندوق النقد أولاً لبدء المبيعات</p>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#openCashRegisterModal">
                        <i class="fas fa-unlock me-2"></i>
                        فتح صندوق النقد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Today's Sales Summary -->
        {% if cash_register and cash_register.is_open %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    ملخص مبيعات اليوم
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ stats.total_sales }}</h4>
                            <small class="text-muted">إجمالي المبيعات</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-success">{{ stats.total_amount }}</h4>
                            <small class="text-muted">إجمالي المبلغ (ر.س)</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ stats.cash_sales }}</h4>
                            <small class="text-muted">المبيعات النقدية (ر.س)</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ stats.card_sales }}</h4>
                            <small class="text-muted">مبيعات البطاقات (ر.س)</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-info" role="progressbar" 
                             style="width: {% widthratio stats.cash_sales stats.total_amount 100 %}%">
                            نقدي
                        </div>
                        <div class="progress-bar bg-warning" role="progressbar" 
                             style="width: {% widthratio stats.card_sales stats.total_amount 100 %}%">
                            بطاقة
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mt-2">
                        <small class="text-muted">نقدي: {{ stats.cash_sales }} ر.س</small>
                        <small class="text-muted">بطاقة: {{ stats.card_sales }} ر.س</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if cash_register and cash_register.is_open %}
                    <a href="{% url 'pos:new_sale' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        بيع جديد
                    </a>
                    <a href="{% url 'pos:sale_list' %}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>
                        قائمة المبيعات
                    </a>
                    <button type="button" class="btn btn-outline-warning" onclick="addCashTransaction()">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة نقد
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="removeCashTransaction()">
                        <i class="fas fa-minus-circle me-2"></i>
                        سحب نقد
                    </button>
                    {% else %}
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#openCashRegisterModal">
                        <i class="fas fa-unlock me-2"></i>
                        فتح صندوق النقد
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Cash Breakdown -->
        {% if cash_register and cash_register.is_open %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    تفصيل النقد
                </h5>
            </div>
            <div class="card-body">
                <div class="cash-breakdown">
                    <div class="row mb-2">
                        <div class="col-6">فئة 500 ر.س:</div>
                        <div class="col-6 text-end">
                            <input type="number" class="form-control form-control-sm" value="0" min="0">
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">فئة 100 ر.س:</div>
                        <div class="col-6 text-end">
                            <input type="number" class="form-control form-control-sm" value="0" min="0">
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">فئة 50 ر.س:</div>
                        <div class="col-6 text-end">
                            <input type="number" class="form-control form-control-sm" value="0" min="0">
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">فئة 20 ر.س:</div>
                        <div class="col-6 text-end">
                            <input type="number" class="form-control form-control-sm" value="0" min="0">
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">فئة 10 ر.س:</div>
                        <div class="col-6 text-end">
                            <input type="number" class="form-control form-control-sm" value="0" min="0">
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">فئة 5 ر.س:</div>
                        <div class="col-6 text-end">
                            <input type="number" class="form-control form-control-sm" value="0" min="0">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6">فئة 1 ر.س:</div>
                        <div class="col-6 text-end">
                            <input type="number" class="form-control form-control-sm" value="0" min="0">
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-6"><strong>الإجمالي:</strong></div>
                        <div class="col-6 text-end">
                            <strong id="totalCash">0.00 ر.س</strong>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-sm btn-outline-primary w-100 mt-3" onclick="calculateCash()">
                        <i class="fas fa-calculator me-2"></i>
                        حساب الإجمالي
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Open Cash Register Modal -->
<div class="modal fade" id="openCashRegisterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">فتح صندوق النقد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'pos:open_cash_register' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="opening_amount" class="form-label">الرصيد الافتتاحي</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="opening_amount" 
                                   name="opening_amount" step="0.01" min="0" value="0" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                        <div class="form-text">أدخل المبلغ الموجود في الصندوق عند بداية الوردية</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-unlock me-2"></i>
                        فتح الصندوق
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Close Cash Register Modal -->
<div class="modal fade" id="closeCashRegisterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إغلاق صندوق النقد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'pos:close_cash_register' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="closing_amount" class="form-label">الرصيد الختامي</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="closing_amount" 
                                   name="closing_amount" step="0.01" min="0" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                        <div class="form-text">أدخل المبلغ الفعلي الموجود في الصندوق</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات حول إغلاق الصندوق..."></textarea>
                    </div>
                    
                    {% if cash_register %}
                    <div class="alert alert-info">
                        <h6>ملخص الوردية:</h6>
                        <ul class="mb-0">
                            <li>الرصيد الافتتاحي: {{ cash_register.opening_amount }} ر.س</li>
                            <li>إجمالي المبيعات: {{ stats.total_amount }} ر.س</li>
                            <li>الرصيد المتوقع: {{ cash_register.current_amount }} ر.س</li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-lock me-2"></i>
                        إغلاق الصندوق
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function calculateCash() {
    const denominations = [500, 100, 50, 20, 10, 5, 1];
    const inputs = document.querySelectorAll('.cash-breakdown input');
    let total = 0;
    
    inputs.forEach((input, index) => {
        const count = parseInt(input.value) || 0;
        total += count * denominations[index];
    });
    
    document.getElementById('totalCash').textContent = total.toFixed(2) + ' ر.س';
}

function addCashTransaction() {
    const amount = prompt('أدخل المبلغ المراد إضافته:');
    if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
        // سيتم إضافة هذه الميزة لاحقاً
        alert('سيتم إضافة هذه الميزة قريباً');
    }
}

function removeCashTransaction() {
    const amount = prompt('أدخل المبلغ المراد سحبه:');
    if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
        // سيتم إضافة هذه الميزة لاحقاً
        alert('سيتم إضافة هذه الميزة قريباً');
    }
}

// حساب تلقائي عند تغيير القيم
document.querySelectorAll('.cash-breakdown input').forEach(input => {
    input.addEventListener('input', calculateCash);
});

// تحديث الصفحة كل دقيقة
setInterval(function() {
    if (!document.hidden) {
        location.reload();
    }
}, 60000);
</script>
{% endblock %}
