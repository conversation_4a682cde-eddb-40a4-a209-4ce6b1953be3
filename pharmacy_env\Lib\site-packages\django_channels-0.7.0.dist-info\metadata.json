{"classifiers": ["Development Status :: 3 - Alpha", "Environment :: Web Environment", "Framework :: Django", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: MIT License", "Operating System :: OS Independent", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: Implementation :: PyPy", "Topic :: Internet :: WWW/HTTP"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON><PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/ymyzk/django-channels"}}}, "extras": ["docs", "test"], "generator": "bdist_wheel (0.30.0.a0)", "license": "MIT", "metadata_version": "2.0", "name": "django-channels", "run_requires": [{"extra": "docs", "requires": ["Sphinx (>=1.4,<1.5)", "sphinx-rtd-theme (>=0.1.9,<0.2)"]}, {"requires": ["requests (>=2.7.0)", "requests-oauthlib (>=0.5.0)", "six (>=1.9.0)"]}, {"environment": "python_version < \"3.3\"", "extra": "test", "requires": ["mock (>=2.0.0,<3.0.0)"]}, {"environment": "python_version < \"3.5\"", "requires": ["typing"]}], "summary": "A Django library for sending notifications", "version": "0.7.0"}