{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"autoFocus\", \"className\", \"classes\", \"value\", \"defaultValue\", \"referenceDate\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onChange\", \"readOnly\", \"shouldDisableYear\", \"disableHighlightToday\", \"onYearFocus\", \"hasFocus\", \"onFocusedViewChange\", \"yearsOrder\", \"yearsPerRow\", \"timezone\", \"gridLabelId\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { shouldForwardProp } from '@mui/system/createStyled';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_useForkRef as useForkRef, unstable_composeClasses as composeClasses, unstable_useControlled as useControlled, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { YearCalendarButton } from \"./YearCalendarButton.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { getYearCalendarUtilityClass } from \"./yearCalendarClasses.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { DIALOG_WIDTH, MAX_CALENDAR_HEIGHT } from \"../internals/constants/dimensions.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { useApplyDefaultValuesToDateValidationProps } from \"../managers/useDateManager.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getYearCalendarUtilityClass, classes);\n};\nfunction useYearCalendarDefaultizedProps(props, name) {\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const validationProps = useApplyDefaultValuesToDateValidationProps(themeProps);\n  return _extends({}, themeProps, validationProps, {\n    yearsPerRow: themeProps.yearsPerRow ?? 3,\n    yearsOrder: themeProps.yearsOrder ?? 'asc'\n  });\n}\nconst YearCalendarRoot = styled('div', {\n  name: 'MuiYearCalendar',\n  slot: 'Root',\n  shouldForwardProp: prop => shouldForwardProp(prop) && prop !== 'yearsPerRow'\n})({\n  display: 'flex',\n  flexWrap: 'wrap',\n  justifyContent: 'space-evenly',\n  rowGap: 12,\n  padding: '6px 0',\n  overflowY: 'auto',\n  height: '100%',\n  width: DIALOG_WIDTH,\n  maxHeight: MAX_CALENDAR_HEIGHT,\n  // avoid padding increasing width over defined\n  boxSizing: 'border-box',\n  position: 'relative',\n  variants: [{\n    props: {\n      yearsPerRow: 3\n    },\n    style: {\n      columnGap: 24\n    }\n  }, {\n    props: {\n      yearsPerRow: 4\n    },\n    style: {\n      columnGap: 0,\n      padding: '0 2px'\n    }\n  }]\n});\nconst YearCalendarButtonFiller = styled('div', {\n  name: 'MuiYearCalendar',\n  slot: 'ButtonFiller'\n})({\n  height: 36,\n  width: 72\n});\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n *\n * API:\n *\n * - [YearCalendar API](https://mui.com/x/api/date-pickers/year-calendar/)\n */\nexport const YearCalendar = /*#__PURE__*/React.forwardRef(function YearCalendar(inProps, ref) {\n  const props = useYearCalendarDefaultizedProps(inProps, 'MuiYearCalendar');\n  const {\n      autoFocus,\n      className,\n      classes: classesProp,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onChange,\n      readOnly,\n      shouldDisableYear,\n      onYearFocus,\n      hasFocus,\n      onFocusedViewChange,\n      yearsOrder,\n      yearsPerRow,\n      timezone: timezoneProp,\n      gridLabelId,\n      slots,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'YearCalendar',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const now = useNow(timezone);\n  const isRtl = useRtl();\n  const utils = useUtils();\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const referenceDate = React.useMemo(() => singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    timezone,\n    referenceDate: referenceDateProp,\n    granularity: SECTION_TYPE_GRANULARITY.year\n  }), [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const classes = useUtilityClasses(classesProp);\n  const todayYear = React.useMemo(() => utils.getYear(now), [utils, now]);\n  const selectedYear = React.useMemo(() => {\n    if (value != null) {\n      return utils.getYear(value);\n    }\n    return null;\n  }, [value, utils]);\n  const [focusedYear, setFocusedYear] = React.useState(() => selectedYear || utils.getYear(referenceDate));\n  const [internalHasFocus, setInternalHasFocus] = useControlled({\n    name: 'YearCalendar',\n    state: 'hasFocus',\n    controlled: hasFocus,\n    default: autoFocus ?? false\n  });\n  const changeHasFocus = useEventCallback(newHasFocus => {\n    setInternalHasFocus(newHasFocus);\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  });\n  const isYearDisabled = React.useCallback(dateToValidate => {\n    if (disablePast && utils.isBeforeYear(dateToValidate, now)) {\n      return true;\n    }\n    if (disableFuture && utils.isAfterYear(dateToValidate, now)) {\n      return true;\n    }\n    if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {\n      return true;\n    }\n    if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {\n      return true;\n    }\n    if (!shouldDisableYear) {\n      return false;\n    }\n    const yearToValidate = utils.startOfYear(dateToValidate);\n    return shouldDisableYear(yearToValidate);\n  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils]);\n  const handleYearSelection = useEventCallback((event, year) => {\n    if (readOnly) {\n      return;\n    }\n    const newDate = utils.setYear(value ?? referenceDate, year);\n    handleValueChange(newDate);\n  });\n  const focusYear = useEventCallback(year => {\n    if (!isYearDisabled(utils.setYear(value ?? referenceDate, year))) {\n      setFocusedYear(year);\n      changeHasFocus(true);\n      onYearFocus?.(year);\n    }\n  });\n  React.useEffect(() => {\n    setFocusedYear(prevFocusedYear => selectedYear !== null && prevFocusedYear !== selectedYear ? selectedYear : prevFocusedYear);\n  }, [selectedYear]);\n  const verticalDirection = yearsOrder !== 'desc' ? yearsPerRow * 1 : yearsPerRow * -1;\n  const horizontalDirection = isRtl && yearsOrder === 'asc' || !isRtl && yearsOrder === 'desc' ? -1 : 1;\n  const handleKeyDown = useEventCallback((event, year) => {\n    switch (event.key) {\n      case 'ArrowUp':\n        focusYear(year - verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusYear(year + verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        focusYear(year - horizontalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowRight':\n        focusYear(year + horizontalDirection);\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  });\n  const handleYearFocus = useEventCallback((event, year) => {\n    focusYear(year);\n  });\n  const handleYearBlur = useEventCallback((event, year) => {\n    if (focusedYear === year) {\n      changeHasFocus(false);\n    }\n  });\n  const scrollerRef = React.useRef(null);\n  const handleRef = useForkRef(ref, scrollerRef);\n  React.useEffect(() => {\n    if (autoFocus || scrollerRef.current === null) {\n      return;\n    }\n    const tabbableButton = scrollerRef.current.querySelector('[tabindex=\"0\"]');\n    if (!tabbableButton) {\n      return;\n    }\n\n    // Taken from useScroll in x-data-grid, but vertically centered\n    const offsetHeight = tabbableButton.offsetHeight;\n    const offsetTop = tabbableButton.offsetTop;\n    const clientHeight = scrollerRef.current.clientHeight;\n    const scrollTop = scrollerRef.current.scrollTop;\n    const elementBottom = offsetTop + offsetHeight;\n    if (offsetHeight > clientHeight || offsetTop < scrollTop) {\n      // Button already visible\n      return;\n    }\n    scrollerRef.current.scrollTop = elementBottom - clientHeight / 2 - offsetHeight / 2;\n  }, [autoFocus]);\n  const yearRange = utils.getYearRange([minDate, maxDate]);\n  if (yearsOrder === 'desc') {\n    yearRange.reverse();\n  }\n  let fillerAmount = yearsPerRow - yearRange.length % yearsPerRow;\n  if (fillerAmount === yearsPerRow) {\n    fillerAmount = 0;\n  }\n  return /*#__PURE__*/_jsxs(YearCalendarRoot, _extends({\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"radiogroup\",\n    \"aria-labelledby\": gridLabelId,\n    yearsPerRow: yearsPerRow\n  }, other, {\n    children: [yearRange.map(year => {\n      const yearNumber = utils.getYear(year);\n      const isSelected = yearNumber === selectedYear;\n      const isDisabled = disabled || isYearDisabled(year);\n      return /*#__PURE__*/_jsx(YearCalendarButton, {\n        selected: isSelected,\n        value: yearNumber,\n        onClick: handleYearSelection,\n        onKeyDown: handleKeyDown,\n        autoFocus: internalHasFocus && yearNumber === focusedYear,\n        disabled: isDisabled,\n        tabIndex: yearNumber === focusedYear && !isDisabled ? 0 : -1,\n        onFocus: handleYearFocus,\n        onBlur: handleYearBlur,\n        \"aria-current\": todayYear === yearNumber ? 'date' : undefined,\n        slots: slots,\n        slotProps: slotProps,\n        classes: classesProp,\n        children: utils.format(year, 'year')\n      }, utils.format(year, 'year'));\n    }), Array.from({\n      length: fillerAmount\n    }, (_, index) => /*#__PURE__*/_jsx(YearCalendarButtonFiller, {}, index))]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") YearCalendar.displayName = \"YearCalendar\";\nprocess.env.NODE_ENV !== \"production\" ? YearCalendar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  gridLabelId: PropTypes.string,\n  hasFocus: PropTypes.bool,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Callback fired when the value changes.\n   * @param {PickerValidDate} value The new value.\n   */\n  onChange: PropTypes.func,\n  onFocusedViewChange: PropTypes.func,\n  onYearFocus: PropTypes.func,\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid year using the validation props, except callbacks such as `shouldDisableYear`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific year.\n   * @param {PickerValidDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n} : void 0;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "useRtl", "shouldForwardProp", "styled", "useThemeProps", "unstable_useForkRef", "useForkRef", "unstable_composeClasses", "composeClasses", "unstable_useControlled", "useControlled", "unstable_useEventCallback", "useEventCallback", "YearCalendarButton", "useUtils", "useNow", "getYearCalendarUtilityClass", "singleItemValueManager", "SECTION_TYPE_GRANULARITY", "useControlledValue", "DIALOG_WIDTH", "MAX_CALENDAR_HEIGHT", "usePickerPrivateContext", "useApplyDefaultValuesToDateValidationProps", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "classes", "slots", "root", "useYearCalendarDefaultizedProps", "props", "name", "themeProps", "validationProps", "yearsPerRow", "yearsOrder", "YearCalendarRoot", "slot", "prop", "display", "flexWrap", "justifyContent", "rowGap", "padding", "overflowY", "height", "width", "maxHeight", "boxSizing", "position", "variants", "style", "columnGap", "YearCalendarButtonFiller", "YearCalendar", "forwardRef", "inProps", "ref", "autoFocus", "className", "classesProp", "value", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onChange", "readOnly", "shouldDisableYear", "onYearFocus", "hasFocus", "onFocusedViewChange", "timezone", "timezoneProp", "gridLabelId", "slotProps", "other", "handleValueChange", "valueManager", "now", "isRtl", "utils", "ownerState", "useMemo", "getInitialReferenceValue", "granularity", "year", "todayYear", "getYear", "selected<PERSON>ear", "focusedYear", "setFocusedYear", "useState", "internalHasFocus", "setInternalHasFocus", "state", "controlled", "default", "changeHasFocus", "newHasFocus", "isYearDisabled", "useCallback", "dateToValidate", "isBeforeYear", "isAfterYear", "yearToValidate", "startOfYear", "handleYearSelection", "event", "newDate", "setYear", "focusYear", "useEffect", "prevFocusedYear", "verticalDirection", "horizontalDirection", "handleKeyDown", "key", "preventDefault", "handleYearFocus", "handleYearBlur", "scrollerRef", "useRef", "handleRef", "current", "tabbableButton", "querySelector", "offsetHeight", "offsetTop", "clientHeight", "scrollTop", "elementBottom", "year<PERSON><PERSON><PERSON>", "getYearRange", "reverse", "fillerAmount", "length", "role", "children", "map", "yearNumber", "isSelected", "isDisabled", "selected", "onClick", "onKeyDown", "tabIndex", "onFocus", "onBlur", "undefined", "format", "Array", "from", "_", "index", "process", "env", "NODE_ENV", "displayName", "propTypes", "bool", "object", "string", "disableHighlightToday", "func", "sx", "oneOfType", "arrayOf", "oneOf"], "sources": ["D:/pos/frontend/node_modules/@mui/x-date-pickers/esm/YearCalendar/YearCalendar.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"autoFocus\", \"className\", \"classes\", \"value\", \"defaultValue\", \"referenceDate\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onChange\", \"readOnly\", \"shouldDisableYear\", \"disableHighlightToday\", \"onYearFocus\", \"hasFocus\", \"onFocusedViewChange\", \"yearsOrder\", \"yearsPerRow\", \"timezone\", \"gridLabelId\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { shouldForwardProp } from '@mui/system/createStyled';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_useForkRef as useForkRef, unstable_composeClasses as composeClasses, unstable_useControlled as useControlled, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { YearCalendarButton } from \"./YearCalendarButton.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { getYearCalendarUtilityClass } from \"./yearCalendarClasses.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { DIALOG_WIDTH, MAX_CALENDAR_HEIGHT } from \"../internals/constants/dimensions.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { useApplyDefaultValuesToDateValidationProps } from \"../managers/useDateManager.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getYearCalendarUtilityClass, classes);\n};\nfunction useYearCalendarDefaultizedProps(props, name) {\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const validationProps = useApplyDefaultValuesToDateValidationProps(themeProps);\n  return _extends({}, themeProps, validationProps, {\n    yearsPerRow: themeProps.yearsPerRow ?? 3,\n    yearsOrder: themeProps.yearsOrder ?? 'asc'\n  });\n}\nconst YearCalendarRoot = styled('div', {\n  name: 'MuiYearCalendar',\n  slot: 'Root',\n  shouldForwardProp: prop => shouldForwardProp(prop) && prop !== 'yearsPerRow'\n})({\n  display: 'flex',\n  flexWrap: 'wrap',\n  justifyContent: 'space-evenly',\n  rowGap: 12,\n  padding: '6px 0',\n  overflowY: 'auto',\n  height: '100%',\n  width: DIALOG_WIDTH,\n  maxHeight: MAX_CALENDAR_HEIGHT,\n  // avoid padding increasing width over defined\n  boxSizing: 'border-box',\n  position: 'relative',\n  variants: [{\n    props: {\n      yearsPerRow: 3\n    },\n    style: {\n      columnGap: 24\n    }\n  }, {\n    props: {\n      yearsPerRow: 4\n    },\n    style: {\n      columnGap: 0,\n      padding: '0 2px'\n    }\n  }]\n});\nconst YearCalendarButtonFiller = styled('div', {\n  name: 'MuiYearCalendar',\n  slot: 'ButtonFiller'\n})({\n  height: 36,\n  width: 72\n});\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n *\n * API:\n *\n * - [YearCalendar API](https://mui.com/x/api/date-pickers/year-calendar/)\n */\nexport const YearCalendar = /*#__PURE__*/React.forwardRef(function YearCalendar(inProps, ref) {\n  const props = useYearCalendarDefaultizedProps(inProps, 'MuiYearCalendar');\n  const {\n      autoFocus,\n      className,\n      classes: classesProp,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onChange,\n      readOnly,\n      shouldDisableYear,\n      onYearFocus,\n      hasFocus,\n      onFocusedViewChange,\n      yearsOrder,\n      yearsPerRow,\n      timezone: timezoneProp,\n      gridLabelId,\n      slots,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'YearCalendar',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const now = useNow(timezone);\n  const isRtl = useRtl();\n  const utils = useUtils();\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const referenceDate = React.useMemo(() => singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    timezone,\n    referenceDate: referenceDateProp,\n    granularity: SECTION_TYPE_GRANULARITY.year\n  }), [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const classes = useUtilityClasses(classesProp);\n  const todayYear = React.useMemo(() => utils.getYear(now), [utils, now]);\n  const selectedYear = React.useMemo(() => {\n    if (value != null) {\n      return utils.getYear(value);\n    }\n    return null;\n  }, [value, utils]);\n  const [focusedYear, setFocusedYear] = React.useState(() => selectedYear || utils.getYear(referenceDate));\n  const [internalHasFocus, setInternalHasFocus] = useControlled({\n    name: 'YearCalendar',\n    state: 'hasFocus',\n    controlled: hasFocus,\n    default: autoFocus ?? false\n  });\n  const changeHasFocus = useEventCallback(newHasFocus => {\n    setInternalHasFocus(newHasFocus);\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  });\n  const isYearDisabled = React.useCallback(dateToValidate => {\n    if (disablePast && utils.isBeforeYear(dateToValidate, now)) {\n      return true;\n    }\n    if (disableFuture && utils.isAfterYear(dateToValidate, now)) {\n      return true;\n    }\n    if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {\n      return true;\n    }\n    if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {\n      return true;\n    }\n    if (!shouldDisableYear) {\n      return false;\n    }\n    const yearToValidate = utils.startOfYear(dateToValidate);\n    return shouldDisableYear(yearToValidate);\n  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils]);\n  const handleYearSelection = useEventCallback((event, year) => {\n    if (readOnly) {\n      return;\n    }\n    const newDate = utils.setYear(value ?? referenceDate, year);\n    handleValueChange(newDate);\n  });\n  const focusYear = useEventCallback(year => {\n    if (!isYearDisabled(utils.setYear(value ?? referenceDate, year))) {\n      setFocusedYear(year);\n      changeHasFocus(true);\n      onYearFocus?.(year);\n    }\n  });\n  React.useEffect(() => {\n    setFocusedYear(prevFocusedYear => selectedYear !== null && prevFocusedYear !== selectedYear ? selectedYear : prevFocusedYear);\n  }, [selectedYear]);\n  const verticalDirection = yearsOrder !== 'desc' ? yearsPerRow * 1 : yearsPerRow * -1;\n  const horizontalDirection = isRtl && yearsOrder === 'asc' || !isRtl && yearsOrder === 'desc' ? -1 : 1;\n  const handleKeyDown = useEventCallback((event, year) => {\n    switch (event.key) {\n      case 'ArrowUp':\n        focusYear(year - verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusYear(year + verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        focusYear(year - horizontalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowRight':\n        focusYear(year + horizontalDirection);\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  });\n  const handleYearFocus = useEventCallback((event, year) => {\n    focusYear(year);\n  });\n  const handleYearBlur = useEventCallback((event, year) => {\n    if (focusedYear === year) {\n      changeHasFocus(false);\n    }\n  });\n  const scrollerRef = React.useRef(null);\n  const handleRef = useForkRef(ref, scrollerRef);\n  React.useEffect(() => {\n    if (autoFocus || scrollerRef.current === null) {\n      return;\n    }\n    const tabbableButton = scrollerRef.current.querySelector('[tabindex=\"0\"]');\n    if (!tabbableButton) {\n      return;\n    }\n\n    // Taken from useScroll in x-data-grid, but vertically centered\n    const offsetHeight = tabbableButton.offsetHeight;\n    const offsetTop = tabbableButton.offsetTop;\n    const clientHeight = scrollerRef.current.clientHeight;\n    const scrollTop = scrollerRef.current.scrollTop;\n    const elementBottom = offsetTop + offsetHeight;\n    if (offsetHeight > clientHeight || offsetTop < scrollTop) {\n      // Button already visible\n      return;\n    }\n    scrollerRef.current.scrollTop = elementBottom - clientHeight / 2 - offsetHeight / 2;\n  }, [autoFocus]);\n  const yearRange = utils.getYearRange([minDate, maxDate]);\n  if (yearsOrder === 'desc') {\n    yearRange.reverse();\n  }\n  let fillerAmount = yearsPerRow - yearRange.length % yearsPerRow;\n  if (fillerAmount === yearsPerRow) {\n    fillerAmount = 0;\n  }\n  return /*#__PURE__*/_jsxs(YearCalendarRoot, _extends({\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"radiogroup\",\n    \"aria-labelledby\": gridLabelId,\n    yearsPerRow: yearsPerRow\n  }, other, {\n    children: [yearRange.map(year => {\n      const yearNumber = utils.getYear(year);\n      const isSelected = yearNumber === selectedYear;\n      const isDisabled = disabled || isYearDisabled(year);\n      return /*#__PURE__*/_jsx(YearCalendarButton, {\n        selected: isSelected,\n        value: yearNumber,\n        onClick: handleYearSelection,\n        onKeyDown: handleKeyDown,\n        autoFocus: internalHasFocus && yearNumber === focusedYear,\n        disabled: isDisabled,\n        tabIndex: yearNumber === focusedYear && !isDisabled ? 0 : -1,\n        onFocus: handleYearFocus,\n        onBlur: handleYearBlur,\n        \"aria-current\": todayYear === yearNumber ? 'date' : undefined,\n        slots: slots,\n        slotProps: slotProps,\n        classes: classesProp,\n        children: utils.format(year, 'year')\n      }, utils.format(year, 'year'));\n    }), Array.from({\n      length: fillerAmount\n    }, (_, index) => /*#__PURE__*/_jsx(YearCalendarButtonFiller, {}, index))]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") YearCalendar.displayName = \"YearCalendar\";\nprocess.env.NODE_ENV !== \"production\" ? YearCalendar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  gridLabelId: PropTypes.string,\n  hasFocus: PropTypes.bool,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Callback fired when the value changes.\n   * @param {PickerValidDate} value The new value.\n   */\n  onChange: PropTypes.func,\n  onFocusedViewChange: PropTypes.func,\n  onYearFocus: PropTypes.func,\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid year using the validation props, except callbacks such as `shouldDisableYear`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific year.\n   * @param {PickerValidDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,aAAa,EAAE,UAAU,EAAE,qBAAqB,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC;AACzW,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,uBAAuB,IAAIC,cAAc,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AACjM,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,QAAQ,EAAEC,MAAM,QAAQ,gCAAgC;AACjE,SAASC,2BAA2B,QAAQ,0BAA0B;AACtE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,sCAAsC;AACxF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,0CAA0C,QAAQ,+BAA+B;AAC1F,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,OAAO,IAAI;EACnC,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOvB,cAAc,CAACsB,KAAK,EAAEd,2BAA2B,EAAEa,OAAO,CAAC;AACpE,CAAC;AACD,SAASG,+BAA+BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACpD,MAAMC,UAAU,GAAG/B,aAAa,CAAC;IAC/B6B,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAME,eAAe,GAAGb,0CAA0C,CAACY,UAAU,CAAC;EAC9E,OAAOvC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,UAAU,EAAEC,eAAe,EAAE;IAC/CC,WAAW,EAAEF,UAAU,CAACE,WAAW,IAAI,CAAC;IACxCC,UAAU,EAAEH,UAAU,CAACG,UAAU,IAAI;EACvC,CAAC,CAAC;AACJ;AACA,MAAMC,gBAAgB,GAAGpC,MAAM,CAAC,KAAK,EAAE;EACrC+B,IAAI,EAAE,iBAAiB;EACvBM,IAAI,EAAE,MAAM;EACZtC,iBAAiB,EAAEuC,IAAI,IAAIvC,iBAAiB,CAACuC,IAAI,CAAC,IAAIA,IAAI,KAAK;AACjE,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,MAAM;EAChBC,cAAc,EAAE,cAAc;EAC9BC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,MAAM;EACjBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE7B,YAAY;EACnB8B,SAAS,EAAE7B,mBAAmB;EAC9B;EACA8B,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,CAAC;IACTpB,KAAK,EAAE;MACLI,WAAW,EAAE;IACf,CAAC;IACDiB,KAAK,EAAE;MACLC,SAAS,EAAE;IACb;EACF,CAAC,EAAE;IACDtB,KAAK,EAAE;MACLI,WAAW,EAAE;IACf,CAAC;IACDiB,KAAK,EAAE;MACLC,SAAS,EAAE,CAAC;MACZT,OAAO,EAAE;IACX;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMU,wBAAwB,GAAGrD,MAAM,CAAC,KAAK,EAAE;EAC7C+B,IAAI,EAAE,iBAAiB;EACvBM,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDQ,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,YAAY,GAAG,aAAa3D,KAAK,CAAC4D,UAAU,CAAC,SAASD,YAAYA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC5F,MAAM3B,KAAK,GAAGD,+BAA+B,CAAC2B,OAAO,EAAE,iBAAiB,CAAC;EACzE,MAAM;MACFE,SAAS;MACTC,SAAS;MACTjC,OAAO,EAAEkC,WAAW;MACpBC,KAAK,EAAEC,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,QAAQ;MACRC,aAAa;MACbC,WAAW;MACXC,OAAO;MACPC,OAAO;MACPC,QAAQ;MACRC,QAAQ;MACRC,iBAAiB;MACjBC,WAAW;MACXC,QAAQ;MACRC,mBAAmB;MACnBzC,UAAU;MACVD,WAAW;MACX2C,QAAQ,EAAEC,YAAY;MACtBC,WAAW;MACXpD,KAAK;MACLqD;IACF,CAAC,GAAGlD,KAAK;IACTmD,KAAK,GAAGzF,6BAA6B,CAACsC,KAAK,EAAEpC,SAAS,CAAC;EACzD,MAAM;IACJmE,KAAK;IACLqB,iBAAiB;IACjBL;EACF,CAAC,GAAG7D,kBAAkB,CAAC;IACrBe,IAAI,EAAE,cAAc;IACpB8C,QAAQ,EAAEC,YAAY;IACtBjB,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCM,QAAQ;IACRY,YAAY,EAAErE;EAChB,CAAC,CAAC;EACF,MAAMsE,GAAG,GAAGxE,MAAM,CAACiE,QAAQ,CAAC;EAC5B,MAAMQ,KAAK,GAAGvF,MAAM,CAAC,CAAC;EACtB,MAAMwF,KAAK,GAAG3E,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJ4E;EACF,CAAC,GAAGpE,uBAAuB,CAAC,CAAC;EAC7B,MAAM6C,aAAa,GAAGrE,KAAK,CAAC6F,OAAO,CAAC,MAAM1E,sBAAsB,CAAC2E,wBAAwB,CAAC;IACxF5B,KAAK;IACLyB,KAAK;IACLxD,KAAK;IACL+C,QAAQ;IACRb,aAAa,EAAEC,iBAAiB;IAChCyB,WAAW,EAAE3E,wBAAwB,CAAC4E;EACxC,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,CAAC;EACD,MAAMjE,OAAO,GAAGD,iBAAiB,CAACmC,WAAW,CAAC;EAC9C,MAAMgC,SAAS,GAAGjG,KAAK,CAAC6F,OAAO,CAAC,MAAMF,KAAK,CAACO,OAAO,CAACT,GAAG,CAAC,EAAE,CAACE,KAAK,EAAEF,GAAG,CAAC,CAAC;EACvE,MAAMU,YAAY,GAAGnG,KAAK,CAAC6F,OAAO,CAAC,MAAM;IACvC,IAAI3B,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOyB,KAAK,CAACO,OAAO,CAAChC,KAAK,CAAC;IAC7B;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACA,KAAK,EAAEyB,KAAK,CAAC,CAAC;EAClB,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGrG,KAAK,CAACsG,QAAQ,CAAC,MAAMH,YAAY,IAAIR,KAAK,CAACO,OAAO,CAAC7B,aAAa,CAAC,CAAC;EACxG,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,aAAa,CAAC;IAC5DwB,IAAI,EAAE,cAAc;IACpBqE,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE1B,QAAQ;IACpB2B,OAAO,EAAE5C,SAAS,IAAI;EACxB,CAAC,CAAC;EACF,MAAM6C,cAAc,GAAG9F,gBAAgB,CAAC+F,WAAW,IAAI;IACrDL,mBAAmB,CAACK,WAAW,CAAC;IAChC,IAAI5B,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC4B,WAAW,CAAC;IAClC;EACF,CAAC,CAAC;EACF,MAAMC,cAAc,GAAG9G,KAAK,CAAC+G,WAAW,CAACC,cAAc,IAAI;IACzD,IAAIvC,WAAW,IAAIkB,KAAK,CAACsB,YAAY,CAACD,cAAc,EAAEvB,GAAG,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAIjB,aAAa,IAAImB,KAAK,CAACuB,WAAW,CAACF,cAAc,EAAEvB,GAAG,CAAC,EAAE;MAC3D,OAAO,IAAI;IACb;IACA,IAAId,OAAO,IAAIgB,KAAK,CAACsB,YAAY,CAACD,cAAc,EAAErC,OAAO,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAID,OAAO,IAAIiB,KAAK,CAACuB,WAAW,CAACF,cAAc,EAAEtC,OAAO,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IACA,IAAI,CAACI,iBAAiB,EAAE;MACtB,OAAO,KAAK;IACd;IACA,MAAMqC,cAAc,GAAGxB,KAAK,CAACyB,WAAW,CAACJ,cAAc,CAAC;IACxD,OAAOlC,iBAAiB,CAACqC,cAAc,CAAC;EAC1C,CAAC,EAAE,CAAC3C,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEc,GAAG,EAAEX,iBAAiB,EAAEa,KAAK,CAAC,CAAC;EACjF,MAAM0B,mBAAmB,GAAGvG,gBAAgB,CAAC,CAACwG,KAAK,EAAEtB,IAAI,KAAK;IAC5D,IAAInB,QAAQ,EAAE;MACZ;IACF;IACA,MAAM0C,OAAO,GAAG5B,KAAK,CAAC6B,OAAO,CAACtD,KAAK,IAAIG,aAAa,EAAE2B,IAAI,CAAC;IAC3DT,iBAAiB,CAACgC,OAAO,CAAC;EAC5B,CAAC,CAAC;EACF,MAAME,SAAS,GAAG3G,gBAAgB,CAACkF,IAAI,IAAI;IACzC,IAAI,CAACc,cAAc,CAACnB,KAAK,CAAC6B,OAAO,CAACtD,KAAK,IAAIG,aAAa,EAAE2B,IAAI,CAAC,CAAC,EAAE;MAChEK,cAAc,CAACL,IAAI,CAAC;MACpBY,cAAc,CAAC,IAAI,CAAC;MACpB7B,WAAW,GAAGiB,IAAI,CAAC;IACrB;EACF,CAAC,CAAC;EACFhG,KAAK,CAAC0H,SAAS,CAAC,MAAM;IACpBrB,cAAc,CAACsB,eAAe,IAAIxB,YAAY,KAAK,IAAI,IAAIwB,eAAe,KAAKxB,YAAY,GAAGA,YAAY,GAAGwB,eAAe,CAAC;EAC/H,CAAC,EAAE,CAACxB,YAAY,CAAC,CAAC;EAClB,MAAMyB,iBAAiB,GAAGpF,UAAU,KAAK,MAAM,GAAGD,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;EACpF,MAAMsF,mBAAmB,GAAGnC,KAAK,IAAIlD,UAAU,KAAK,KAAK,IAAI,CAACkD,KAAK,IAAIlD,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;EACrG,MAAMsF,aAAa,GAAGhH,gBAAgB,CAAC,CAACwG,KAAK,EAAEtB,IAAI,KAAK;IACtD,QAAQsB,KAAK,CAACS,GAAG;MACf,KAAK,SAAS;QACZN,SAAS,CAACzB,IAAI,GAAG4B,iBAAiB,CAAC;QACnCN,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,WAAW;QACdP,SAAS,CAACzB,IAAI,GAAG4B,iBAAiB,CAAC;QACnCN,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,WAAW;QACdP,SAAS,CAACzB,IAAI,GAAG6B,mBAAmB,CAAC;QACrCP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,YAAY;QACfP,SAAS,CAACzB,IAAI,GAAG6B,mBAAmB,CAAC;QACrCP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF;QACE;IACJ;EACF,CAAC,CAAC;EACF,MAAMC,eAAe,GAAGnH,gBAAgB,CAAC,CAACwG,KAAK,EAAEtB,IAAI,KAAK;IACxDyB,SAAS,CAACzB,IAAI,CAAC;EACjB,CAAC,CAAC;EACF,MAAMkC,cAAc,GAAGpH,gBAAgB,CAAC,CAACwG,KAAK,EAAEtB,IAAI,KAAK;IACvD,IAAII,WAAW,KAAKJ,IAAI,EAAE;MACxBY,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMuB,WAAW,GAAGnI,KAAK,CAACoI,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMC,SAAS,GAAG7H,UAAU,CAACsD,GAAG,EAAEqE,WAAW,CAAC;EAC9CnI,KAAK,CAAC0H,SAAS,CAAC,MAAM;IACpB,IAAI3D,SAAS,IAAIoE,WAAW,CAACG,OAAO,KAAK,IAAI,EAAE;MAC7C;IACF;IACA,MAAMC,cAAc,GAAGJ,WAAW,CAACG,OAAO,CAACE,aAAa,CAAC,gBAAgB,CAAC;IAC1E,IAAI,CAACD,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAME,YAAY,GAAGF,cAAc,CAACE,YAAY;IAChD,MAAMC,SAAS,GAAGH,cAAc,CAACG,SAAS;IAC1C,MAAMC,YAAY,GAAGR,WAAW,CAACG,OAAO,CAACK,YAAY;IACrD,MAAMC,SAAS,GAAGT,WAAW,CAACG,OAAO,CAACM,SAAS;IAC/C,MAAMC,aAAa,GAAGH,SAAS,GAAGD,YAAY;IAC9C,IAAIA,YAAY,GAAGE,YAAY,IAAID,SAAS,GAAGE,SAAS,EAAE;MACxD;MACA;IACF;IACAT,WAAW,CAACG,OAAO,CAACM,SAAS,GAAGC,aAAa,GAAGF,YAAY,GAAG,CAAC,GAAGF,YAAY,GAAG,CAAC;EACrF,CAAC,EAAE,CAAC1E,SAAS,CAAC,CAAC;EACf,MAAM+E,SAAS,GAAGnD,KAAK,CAACoD,YAAY,CAAC,CAACpE,OAAO,EAAED,OAAO,CAAC,CAAC;EACxD,IAAIlC,UAAU,KAAK,MAAM,EAAE;IACzBsG,SAAS,CAACE,OAAO,CAAC,CAAC;EACrB;EACA,IAAIC,YAAY,GAAG1G,WAAW,GAAGuG,SAAS,CAACI,MAAM,GAAG3G,WAAW;EAC/D,IAAI0G,YAAY,KAAK1G,WAAW,EAAE;IAChC0G,YAAY,GAAG,CAAC;EAClB;EACA,OAAO,aAAapH,KAAK,CAACY,gBAAgB,EAAE3C,QAAQ,CAAC;IACnDgE,GAAG,EAAEuE,SAAS;IACdrE,SAAS,EAAE9D,IAAI,CAAC6B,OAAO,CAACE,IAAI,EAAE+B,SAAS,CAAC;IACxC4B,UAAU,EAAEA,UAAU;IACtBuD,IAAI,EAAE,YAAY;IAClB,iBAAiB,EAAE/D,WAAW;IAC9B7C,WAAW,EAAEA;EACf,CAAC,EAAE+C,KAAK,EAAE;IACR8D,QAAQ,EAAE,CAACN,SAAS,CAACO,GAAG,CAACrD,IAAI,IAAI;MAC/B,MAAMsD,UAAU,GAAG3D,KAAK,CAACO,OAAO,CAACF,IAAI,CAAC;MACtC,MAAMuD,UAAU,GAAGD,UAAU,KAAKnD,YAAY;MAC9C,MAAMqD,UAAU,GAAGjF,QAAQ,IAAIuC,cAAc,CAACd,IAAI,CAAC;MACnD,OAAO,aAAarE,IAAI,CAACZ,kBAAkB,EAAE;QAC3C0I,QAAQ,EAAEF,UAAU;QACpBrF,KAAK,EAAEoF,UAAU;QACjBI,OAAO,EAAErC,mBAAmB;QAC5BsC,SAAS,EAAE7B,aAAa;QACxB/D,SAAS,EAAEwC,gBAAgB,IAAI+C,UAAU,KAAKlD,WAAW;QACzD7B,QAAQ,EAAEiF,UAAU;QACpBI,QAAQ,EAAEN,UAAU,KAAKlD,WAAW,IAAI,CAACoD,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5DK,OAAO,EAAE5B,eAAe;QACxB6B,MAAM,EAAE5B,cAAc;QACtB,cAAc,EAAEjC,SAAS,KAAKqD,UAAU,GAAG,MAAM,GAAGS,SAAS;QAC7D/H,KAAK,EAAEA,KAAK;QACZqD,SAAS,EAAEA,SAAS;QACpBtD,OAAO,EAAEkC,WAAW;QACpBmF,QAAQ,EAAEzD,KAAK,CAACqE,MAAM,CAAChE,IAAI,EAAE,MAAM;MACrC,CAAC,EAAEL,KAAK,CAACqE,MAAM,CAAChE,IAAI,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC,EAAEiE,KAAK,CAACC,IAAI,CAAC;MACbhB,MAAM,EAAED;IACV,CAAC,EAAE,CAACkB,CAAC,EAAEC,KAAK,KAAK,aAAazI,IAAI,CAAC+B,wBAAwB,EAAE,CAAC,CAAC,EAAE0G,KAAK,CAAC,CAAC;EAC1E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE5G,YAAY,CAAC6G,WAAW,GAAG,cAAc;AACpFH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5G,YAAY,CAAC8G,SAAS,GAAG;EAC/D;EACA;EACA;EACA;EACA1G,SAAS,EAAE9D,SAAS,CAACyK,IAAI;EACzB;AACF;AACA;EACE3I,OAAO,EAAE9B,SAAS,CAAC0K,MAAM;EACzB3G,SAAS,EAAE/D,SAAS,CAAC2K,MAAM;EAC3B;AACF;AACA;AACA;EACExG,YAAY,EAAEnE,SAAS,CAAC0K,MAAM;EAC9B;AACF;AACA;AACA;AACA;EACEpG,QAAQ,EAAEtE,SAAS,CAACyK,IAAI;EACxB;AACF;AACA;AACA;EACElG,aAAa,EAAEvE,SAAS,CAACyK,IAAI;EAC7B;AACF;AACA;AACA;EACEG,qBAAqB,EAAE5K,SAAS,CAACyK,IAAI;EACrC;AACF;AACA;AACA;EACEjG,WAAW,EAAExE,SAAS,CAACyK,IAAI;EAC3BtF,WAAW,EAAEnF,SAAS,CAAC2K,MAAM;EAC7B5F,QAAQ,EAAE/E,SAAS,CAACyK,IAAI;EACxB;AACF;AACA;AACA;EACEhG,OAAO,EAAEzE,SAAS,CAAC0K,MAAM;EACzB;AACF;AACA;AACA;EACEhG,OAAO,EAAE1E,SAAS,CAAC0K,MAAM;EACzB;AACF;AACA;AACA;EACE/F,QAAQ,EAAE3E,SAAS,CAAC6K,IAAI;EACxB7F,mBAAmB,EAAEhF,SAAS,CAAC6K,IAAI;EACnC/F,WAAW,EAAE9E,SAAS,CAAC6K,IAAI;EAC3B;AACF;AACA;AACA;AACA;EACEjG,QAAQ,EAAE5E,SAAS,CAACyK,IAAI;EACxB;AACF;AACA;AACA;EACErG,aAAa,EAAEpE,SAAS,CAAC0K,MAAM;EAC/B;AACF;AACA;AACA;AACA;EACE7F,iBAAiB,EAAE7E,SAAS,CAAC6K,IAAI;EACjC;AACF;AACA;AACA;EACEzF,SAAS,EAAEpF,SAAS,CAAC0K,MAAM;EAC3B;AACF;AACA;AACA;EACE3I,KAAK,EAAE/B,SAAS,CAAC0K,MAAM;EACvB;AACF;AACA;EACEI,EAAE,EAAE9K,SAAS,CAAC+K,SAAS,CAAC,CAAC/K,SAAS,CAACgL,OAAO,CAAChL,SAAS,CAAC+K,SAAS,CAAC,CAAC/K,SAAS,CAAC6K,IAAI,EAAE7K,SAAS,CAAC0K,MAAM,EAAE1K,SAAS,CAACyK,IAAI,CAAC,CAAC,CAAC,EAAEzK,SAAS,CAAC6K,IAAI,EAAE7K,SAAS,CAAC0K,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACEzF,QAAQ,EAAEjF,SAAS,CAAC2K,MAAM;EAC1B;AACF;AACA;AACA;EACE1G,KAAK,EAAEjE,SAAS,CAAC0K,MAAM;EACvB;AACF;AACA;AACA;AACA;EACEnI,UAAU,EAAEvC,SAAS,CAACiL,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC5C;AACF;AACA;AACA;EACE3I,WAAW,EAAEtC,SAAS,CAACiL,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACrC,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}