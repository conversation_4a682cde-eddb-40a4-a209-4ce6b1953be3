# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
        ('inventory', '0001_initial'),
        ('suppliers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='batch',
            name='purchase_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='suppliers.purchaseorder', verbose_name='أمر الشراء'),
        ),
        migrations.AddField(
            model_name='batch',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='suppliers.supplier', verbose_name='المورد'),
        ),
        migrations.AddField(
            model_name='category',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='inventory.category', verbose_name='الفئة الأب'),
        ),
        migrations.AddField(
            model_name='medicine',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='medicines', to='inventory.category', verbose_name='الفئة'),
        ),
        migrations.AddField(
            model_name='medicine',
            name='manufacturer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='medicines', to='inventory.manufacturer', verbose_name='الشركة المصنعة'),
        ),
        migrations.AddField(
            model_name='batch',
            name='medicine',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='batches', to='inventory.medicine', verbose_name='الدواء'),
        ),
        migrations.AddField(
            model_name='stockalert',
            name='acknowledged_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الاطلاع بواسطة'),
        ),
        migrations.AddField(
            model_name='stockalert',
            name='branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_alerts', to='branches.branch', verbose_name='الفرع'),
        ),
        migrations.AddField(
            model_name='stockalert',
            name='medicine',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='inventory.medicine', verbose_name='الدواء'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='batch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.batch', verbose_name='الدفعة'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم'),
        ),
        migrations.AlterUniqueTogether(
            name='batch',
            unique_together={('medicine', 'branch', 'batch_number')},
        ),
    ]
