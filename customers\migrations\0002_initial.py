# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
        ('customers', '0001_initial'),
        ('inventory', '0001_initial'),
        ('pos', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='loyaltytransaction',
            name='sale',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='pos.sale', verbose_name='البيع'),
        ),
        migrations.AddField(
            model_name='prescription',
            name='branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='prescriptions', to='branches.branch', verbose_name='الفرع'),
        ),
        migrations.AddField(
            model_name='prescription',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='prescriptions', to='customers.customer', verbose_name='المريض'),
        ),
        migrations.AddField(
            model_name='prescription',
            name='dispensed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dispensed_prescriptions', to=settings.AUTH_USER_MODEL, verbose_name='تم الصرف بواسطة'),
        ),
        migrations.AddField(
            model_name='prescription',
            name='doctor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='prescriptions', to='customers.doctor', verbose_name='الطبيب'),
        ),
        migrations.AddField(
            model_name='prescriptionitem',
            name='medicine',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.medicine', verbose_name='الدواء'),
        ),
        migrations.AddField(
            model_name='prescriptionitem',
            name='prescription',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='customers.prescription', verbose_name='الوصفة'),
        ),
    ]
