{"ast": null, "code": "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "map": {"version": 3, "names": ["popperGenerator", "detectOverflow", "eventListeners", "popperOffsets", "computeStyles", "applyStyles", "defaultModifiers", "createPopper"], "sources": ["D:/pos/frontend/node_modules/@popperjs/core/lib/popper-lite.js"], "sourcesContent": ["import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,QAAQ,mBAAmB;AACnE,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,IAAIC,gBAAgB,GAAG,CAACJ,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,WAAW,CAAC;AAClF,IAAIE,YAAY,GAAG,aAAaP,eAAe,CAAC;EAC9CM,gBAAgB,EAAEA;AACpB,CAAC,CAAC,CAAC,CAAC;;AAEJ,SAASC,YAAY,EAAEP,eAAe,EAAEM,gBAAgB,EAAEL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}