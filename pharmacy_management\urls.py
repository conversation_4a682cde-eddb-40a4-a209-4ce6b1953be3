"""
URL configuration for pharmacy_management project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView

# تخصيص لوحة الإدارة
admin.site.site_header = "نظام إدارة الصيدليات"
admin.site.site_title = "إدارة الصيدليات"
admin.site.index_title = "لوحة التحكم الرئيسية"

urlpatterns = [
    # Admin URLs
    path('admin/', admin.site.urls),

    # Redirect root to admin for now
    path('', RedirectView.as_view(url='/admin/', permanent=False)),

    # API URLs
    path('api/', include('api.urls')),

    # App URLs (for future development)
    # path('pos/', include('pos.urls')),
    # path('inventory/', include('inventory.urls')),
    # path('customers/', include('customers.urls')),
    # path('suppliers/', include('suppliers.urls')),
    # path('reports/', include('reports.urls')),
    # path('branches/', include('branches.urls')),
    # path('settings/', include('settings.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
