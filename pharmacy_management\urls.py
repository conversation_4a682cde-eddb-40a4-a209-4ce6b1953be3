"""
URL configuration for pharmacy_management project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from . import views

# تخصيص لوحة الإدارة
admin.site.site_header = "نظام إدارة الصيدليات"
admin.site.site_title = "إدارة الصيدليات"
admin.site.index_title = "لوحة التحكم الرئيسية"

urlpatterns = [
    # الصفحة الرئيسية
    path('', views.dashboard_view, name='home'),
    path('dashboard/', views.dashboard_view, name='dashboard'),

    # المصادقة
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('logout/', auth_views.LogoutView.as_view(next_page='login'), name='logout'),
    path('profile/', views.profile_view, name='profile'),

    # البحث والإشعارات
    path('search/', views.search_view, name='search'),
    path('notifications/', views.notifications_view, name='notifications'),
    path('api/quick-stats/', views.quick_stats_api, name='quick_stats_api'),

    # لوحة الإدارة
    path('admin/', admin.site.urls),

    # تطبيقات النظام
    path('pos/', include('pos.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
