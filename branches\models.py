from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator


class Branch(models.Model):
    """
    Branch/Pharmacy location model
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الفرع')
    )

    code = models.CharField(
        max_length=10,
        unique=True,
        verbose_name=_('رمز الفرع')
    )

    address = models.TextField(
        verbose_name=_('العنوان')
    )

    city = models.CharField(
        max_length=50,
        verbose_name=_('المدينة')
    )

    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message=_("رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    )

    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        verbose_name=_('رقم الهاتف')
    )

    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name=_('البريد الإلكتروني')
    )

    manager = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_branches',
        verbose_name=_('المدير')
    )

    license_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('رقم الترخيص')
    )

    license_expiry_date = models.DateField(
        verbose_name=_('تاريخ انتهاء الترخيص')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    is_main_branch = models.BooleanField(
        default=False,
        verbose_name=_('الفرع الرئيسي')
    )

    opening_time = models.TimeField(
        verbose_name=_('وقت الافتتاح')
    )

    closing_time = models.TimeField(
        verbose_name=_('وقت الإغلاق')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('فرع')
        verbose_name_plural = _('الفروع')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Ensure only one main branch
        if self.is_main_branch:
            Branch.objects.filter(is_main_branch=True).update(is_main_branch=False)
        super().save(*args, **kwargs)


class BranchSettings(models.Model):
    """
    Branch-specific settings and configurations
    """
    branch = models.OneToOneField(
        Branch,
        on_delete=models.CASCADE,
        related_name='settings',
        verbose_name=_('الفرع')
    )

    # Tax settings
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=15.00,
        verbose_name=_('معدل الضريبة (%)')
    )

    # Currency settings
    currency_code = models.CharField(
        max_length=3,
        default='SAR',
        verbose_name=_('رمز العملة')
    )

    currency_symbol = models.CharField(
        max_length=5,
        default='ر.س',
        verbose_name=_('رمز العملة')
    )

    # Receipt settings
    receipt_header = models.TextField(
        blank=True,
        verbose_name=_('رأس الفاتورة')
    )

    receipt_footer = models.TextField(
        blank=True,
        verbose_name=_('ذيل الفاتورة')
    )

    # Inventory settings
    low_stock_threshold = models.IntegerField(
        default=10,
        verbose_name=_('حد التنبيه للمخزون المنخفض')
    )

    expiry_alert_days = models.IntegerField(
        default=30,
        verbose_name=_('أيام التنبيه قبل انتهاء الصلاحية')
    )

    # POS settings
    auto_print_receipt = models.BooleanField(
        default=True,
        verbose_name=_('طباعة الفاتورة تلقائياً')
    )

    allow_negative_stock = models.BooleanField(
        default=False,
        verbose_name=_('السماح بالمخزون السالب')
    )

    # Backup settings
    auto_backup_enabled = models.BooleanField(
        default=True,
        verbose_name=_('النسخ الاحتياطي التلقائي')
    )

    backup_frequency_hours = models.IntegerField(
        default=24,
        verbose_name=_('تكرار النسخ الاحتياطي (ساعات)')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('إعدادات الفرع')
        verbose_name_plural = _('إعدادات الفروع')

    def __str__(self):
        return f"إعدادات {self.branch.name}"
