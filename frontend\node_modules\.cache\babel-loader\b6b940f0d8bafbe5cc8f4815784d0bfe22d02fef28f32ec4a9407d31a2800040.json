{"ast": null, "code": "import { areViewsEqual } from \"./views.js\";\nexport const EXPORTED_TIME_VIEWS = ['hours', 'minutes', 'seconds'];\nexport const TIME_VIEWS = ['hours', 'minutes', 'seconds', 'meridiem'];\nexport const isTimeView = view => EXPORTED_TIME_VIEWS.includes(view);\nexport const isInternalTimeView = view => TIME_VIEWS.includes(view);\nexport const getMeridiem = (date, utils) => {\n  if (!date) {\n    return null;\n  }\n  return utils.getHours(date) >= 12 ? 'pm' : 'am';\n};\nexport const convertValueToMeridiem = (value, meridiem, ampm) => {\n  if (ampm) {\n    const currentMeridiem = value >= 12 ? 'pm' : 'am';\n    if (currentMeridiem !== meridiem) {\n      return meridiem === 'am' ? value - 12 : value + 12;\n    }\n  }\n  return value;\n};\nexport const convertToMeridiem = (time, meridiem, ampm, utils) => {\n  const newHoursAmount = convertValueToMeridiem(utils.getHours(time), meridiem, ampm);\n  return utils.setHours(time, newHoursAmount);\n};\nexport const getSecondsInDay = (date, utils) => {\n  return utils.getHours(date) * 3600 + utils.getMinutes(date) * 60 + utils.getSeconds(date);\n};\nexport const createIsAfterIgnoreDatePart = (disableIgnoringDatePartForTimeValidation, utils) => (dateLeft, dateRight) => {\n  if (disableIgnoringDatePartForTimeValidation) {\n    return utils.isAfter(dateLeft, dateRight);\n  }\n  return getSecondsInDay(dateLeft, utils) > getSecondsInDay(dateRight, utils);\n};\nexport const resolveTimeFormat = (utils, {\n  format,\n  views,\n  ampm\n}) => {\n  if (format != null) {\n    return format;\n  }\n  const formats = utils.formats;\n  if (areViewsEqual(views, ['hours'])) {\n    return ampm ? `${formats.hours12h} ${formats.meridiem}` : formats.hours24h;\n  }\n  if (areViewsEqual(views, ['minutes'])) {\n    return formats.minutes;\n  }\n  if (areViewsEqual(views, ['seconds'])) {\n    return formats.seconds;\n  }\n  if (areViewsEqual(views, ['minutes', 'seconds'])) {\n    return `${formats.minutes}:${formats.seconds}`;\n  }\n  if (areViewsEqual(views, ['hours', 'minutes', 'seconds'])) {\n    return ampm ? `${formats.hours12h}:${formats.minutes}:${formats.seconds} ${formats.meridiem}` : `${formats.hours24h}:${formats.minutes}:${formats.seconds}`;\n  }\n  return ampm ? `${formats.hours12h}:${formats.minutes} ${formats.meridiem}` : `${formats.hours24h}:${formats.minutes}`;\n};", "map": {"version": 3, "names": ["areViewsEqual", "EXPORTED_TIME_VIEWS", "TIME_VIEWS", "isTimeView", "view", "includes", "isInternalTimeView", "getMeridiem", "date", "utils", "getHours", "convertValueToMeridiem", "value", "meridiem", "ampm", "currentMeridiem", "convertToMeridiem", "time", "newHoursAmount", "setHours", "getSecondsInDay", "getMinutes", "getSeconds", "createIsAfterIgnoreDatePart", "disableIgnoringDatePartForTimeValidation", "dateLeft", "dateRight", "isAfter", "resolveTimeFormat", "format", "views", "formats", "hours12h", "hours24h", "minutes", "seconds"], "sources": ["D:/pos/frontend/node_modules/@mui/x-date-pickers/esm/internals/utils/time-utils.js"], "sourcesContent": ["import { areViewsEqual } from \"./views.js\";\nexport const EXPORTED_TIME_VIEWS = ['hours', 'minutes', 'seconds'];\nexport const TIME_VIEWS = ['hours', 'minutes', 'seconds', 'meridiem'];\nexport const isTimeView = view => EXPORTED_TIME_VIEWS.includes(view);\nexport const isInternalTimeView = view => TIME_VIEWS.includes(view);\nexport const getMeridiem = (date, utils) => {\n  if (!date) {\n    return null;\n  }\n  return utils.getHours(date) >= 12 ? 'pm' : 'am';\n};\nexport const convertValueToMeridiem = (value, meridiem, ampm) => {\n  if (ampm) {\n    const currentMeridiem = value >= 12 ? 'pm' : 'am';\n    if (currentMeridiem !== meridiem) {\n      return meridiem === 'am' ? value - 12 : value + 12;\n    }\n  }\n  return value;\n};\nexport const convertToMeridiem = (time, meridiem, ampm, utils) => {\n  const newHoursAmount = convertValueToMeridiem(utils.getHours(time), meridiem, ampm);\n  return utils.setHours(time, newHoursAmount);\n};\nexport const getSecondsInDay = (date, utils) => {\n  return utils.getHours(date) * 3600 + utils.getMinutes(date) * 60 + utils.getSeconds(date);\n};\nexport const createIsAfterIgnoreDatePart = (disableIgnoringDatePartForTimeValidation, utils) => (dateLeft, dateRight) => {\n  if (disableIgnoringDatePartForTimeValidation) {\n    return utils.isAfter(dateLeft, dateRight);\n  }\n  return getSecondsInDay(dateLeft, utils) > getSecondsInDay(dateRight, utils);\n};\nexport const resolveTimeFormat = (utils, {\n  format,\n  views,\n  ampm\n}) => {\n  if (format != null) {\n    return format;\n  }\n  const formats = utils.formats;\n  if (areViewsEqual(views, ['hours'])) {\n    return ampm ? `${formats.hours12h} ${formats.meridiem}` : formats.hours24h;\n  }\n  if (areViewsEqual(views, ['minutes'])) {\n    return formats.minutes;\n  }\n  if (areViewsEqual(views, ['seconds'])) {\n    return formats.seconds;\n  }\n  if (areViewsEqual(views, ['minutes', 'seconds'])) {\n    return `${formats.minutes}:${formats.seconds}`;\n  }\n  if (areViewsEqual(views, ['hours', 'minutes', 'seconds'])) {\n    return ampm ? `${formats.hours12h}:${formats.minutes}:${formats.seconds} ${formats.meridiem}` : `${formats.hours24h}:${formats.minutes}:${formats.seconds}`;\n  }\n  return ampm ? `${formats.hours12h}:${formats.minutes} ${formats.meridiem}` : `${formats.hours24h}:${formats.minutes}`;\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAY;AAC1C,OAAO,MAAMC,mBAAmB,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;AAClE,OAAO,MAAMC,UAAU,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;AACrE,OAAO,MAAMC,UAAU,GAAGC,IAAI,IAAIH,mBAAmB,CAACI,QAAQ,CAACD,IAAI,CAAC;AACpE,OAAO,MAAME,kBAAkB,GAAGF,IAAI,IAAIF,UAAU,CAACG,QAAQ,CAACD,IAAI,CAAC;AACnE,OAAO,MAAMG,WAAW,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;EAC1C,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,OAAOC,KAAK,CAACC,QAAQ,CAACF,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;AACjD,CAAC;AACD,OAAO,MAAMG,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,KAAK;EAC/D,IAAIA,IAAI,EAAE;IACR,MAAMC,eAAe,GAAGH,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;IACjD,IAAIG,eAAe,KAAKF,QAAQ,EAAE;MAChC,OAAOA,QAAQ,KAAK,IAAI,GAAGD,KAAK,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE;IACpD;EACF;EACA,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,MAAMI,iBAAiB,GAAGA,CAACC,IAAI,EAAEJ,QAAQ,EAAEC,IAAI,EAAEL,KAAK,KAAK;EAChE,MAAMS,cAAc,GAAGP,sBAAsB,CAACF,KAAK,CAACC,QAAQ,CAACO,IAAI,CAAC,EAAEJ,QAAQ,EAAEC,IAAI,CAAC;EACnF,OAAOL,KAAK,CAACU,QAAQ,CAACF,IAAI,EAAEC,cAAc,CAAC;AAC7C,CAAC;AACD,OAAO,MAAME,eAAe,GAAGA,CAACZ,IAAI,EAAEC,KAAK,KAAK;EAC9C,OAAOA,KAAK,CAACC,QAAQ,CAACF,IAAI,CAAC,GAAG,IAAI,GAAGC,KAAK,CAACY,UAAU,CAACb,IAAI,CAAC,GAAG,EAAE,GAAGC,KAAK,CAACa,UAAU,CAACd,IAAI,CAAC;AAC3F,CAAC;AACD,OAAO,MAAMe,2BAA2B,GAAGA,CAACC,wCAAwC,EAAEf,KAAK,KAAK,CAACgB,QAAQ,EAAEC,SAAS,KAAK;EACvH,IAAIF,wCAAwC,EAAE;IAC5C,OAAOf,KAAK,CAACkB,OAAO,CAACF,QAAQ,EAAEC,SAAS,CAAC;EAC3C;EACA,OAAON,eAAe,CAACK,QAAQ,EAAEhB,KAAK,CAAC,GAAGW,eAAe,CAACM,SAAS,EAAEjB,KAAK,CAAC;AAC7E,CAAC;AACD,OAAO,MAAMmB,iBAAiB,GAAGA,CAACnB,KAAK,EAAE;EACvCoB,MAAM;EACNC,KAAK;EACLhB;AACF,CAAC,KAAK;EACJ,IAAIe,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOA,MAAM;EACf;EACA,MAAME,OAAO,GAAGtB,KAAK,CAACsB,OAAO;EAC7B,IAAI/B,aAAa,CAAC8B,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE;IACnC,OAAOhB,IAAI,GAAG,GAAGiB,OAAO,CAACC,QAAQ,IAAID,OAAO,CAAClB,QAAQ,EAAE,GAAGkB,OAAO,CAACE,QAAQ;EAC5E;EACA,IAAIjC,aAAa,CAAC8B,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE;IACrC,OAAOC,OAAO,CAACG,OAAO;EACxB;EACA,IAAIlC,aAAa,CAAC8B,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE;IACrC,OAAOC,OAAO,CAACI,OAAO;EACxB;EACA,IAAInC,aAAa,CAAC8B,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;IAChD,OAAO,GAAGC,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACI,OAAO,EAAE;EAChD;EACA,IAAInC,aAAa,CAAC8B,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;IACzD,OAAOhB,IAAI,GAAG,GAAGiB,OAAO,CAACC,QAAQ,IAAID,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAAClB,QAAQ,EAAE,GAAG,GAAGkB,OAAO,CAACE,QAAQ,IAAIF,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACI,OAAO,EAAE;EAC7J;EACA,OAAOrB,IAAI,GAAG,GAAGiB,OAAO,CAACC,QAAQ,IAAID,OAAO,CAACG,OAAO,IAAIH,OAAO,CAAClB,QAAQ,EAAE,GAAG,GAAGkB,OAAO,CAACE,QAAQ,IAAIF,OAAO,CAACG,OAAO,EAAE;AACvH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}