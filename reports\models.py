from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import json

User = get_user_model()


class ReportTemplate(models.Model):
    """
    Report templates for different types of reports
    """
    REPORT_TYPES = [
        ('sales', _('تقارير المبيعات')),
        ('inventory', _('تقارير المخزون')),
        ('financial', _('التقارير المالية')),
        ('customer', _('تقارير العملاء')),
        ('supplier', _('تقارير الموردين')),
        ('prescription', _('تقارير الوصفات')),
        ('audit', _('تقارير المراجعة')),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم التقرير')
    )

    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        verbose_name=_('نوع التقرير')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('الوصف')
    )

    # Report configuration stored as JSON
    configuration = models.JSONField(
        default=dict,
        verbose_name=_('إعدادات التقرير')
    )

    # SQL query for the report
    query = models.TextField(
        verbose_name=_('استعلام التقرير')
    )

    # Report layout/template
    template_file = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('ملف القالب')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        verbose_name=_('أنشئ بواسطة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('قالب تقرير')
        verbose_name_plural = _('قوالب التقارير')
        ordering = ['report_type', 'name']

    def __str__(self):
        return f"{self.get_report_type_display()} - {self.name}"


class GeneratedReport(models.Model):
    """
    Generated reports history
    """
    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('processing', _('قيد المعالجة')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
    ]

    FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
        ('html', 'HTML'),
    ]

    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.PROTECT,
        related_name='generated_reports',
        verbose_name=_('قالب التقرير')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('اسم التقرير')
    )

    # Report parameters stored as JSON
    parameters = models.JSONField(
        default=dict,
        verbose_name=_('معاملات التقرير')
    )

    # Date range for the report
    date_from = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('من تاريخ')
    )

    date_to = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('إلى تاريخ')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('الفرع')
    )

    format = models.CharField(
        max_length=10,
        choices=FORMAT_CHOICES,
        default='pdf',
        verbose_name=_('التنسيق')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    file_path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('مسار الملف')
    )

    file_size = models.BigIntegerField(
        null=True,
        blank=True,
        verbose_name=_('حجم الملف')
    )

    error_message = models.TextField(
        blank=True,
        verbose_name=_('رسالة الخطأ')
    )

    generated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        verbose_name=_('أنشئ بواسطة')
    )

    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الإكمال')
    )

    class Meta:
        verbose_name = _('تقرير منشأ')
        verbose_name_plural = _('التقارير المنشأة')
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.name} - {self.generated_at.date()}"


class ScheduledReport(models.Model):
    """
    Scheduled reports for automatic generation
    """
    FREQUENCY_CHOICES = [
        ('daily', _('يومي')),
        ('weekly', _('أسبوعي')),
        ('monthly', _('شهري')),
        ('quarterly', _('ربع سنوي')),
        ('yearly', _('سنوي')),
    ]

    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='scheduled_reports',
        verbose_name=_('قالب التقرير')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('اسم الجدولة')
    )

    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        verbose_name=_('التكرار')
    )

    # Report parameters stored as JSON
    parameters = models.JSONField(
        default=dict,
        verbose_name=_('معاملات التقرير')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('الفرع')
    )

    format = models.CharField(
        max_length=10,
        choices=GeneratedReport.FORMAT_CHOICES,
        default='pdf',
        verbose_name=_('التنسيق')
    )

    # Email settings
    email_recipients = models.TextField(
        help_text=_('عناوين البريد الإلكتروني مفصولة بفواصل'),
        verbose_name=_('المستلمون')
    )

    email_subject = models.CharField(
        max_length=200,
        verbose_name=_('موضوع البريد')
    )

    email_body = models.TextField(
        blank=True,
        verbose_name=_('نص البريد')
    )

    # Scheduling
    next_run = models.DateTimeField(
        verbose_name=_('التشغيل التالي')
    )

    last_run = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('آخر تشغيل')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        verbose_name=_('أنشئ بواسطة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('تقرير مجدول')
        verbose_name_plural = _('التقارير المجدولة')
        ordering = ['next_run']

    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"


class DashboardWidget(models.Model):
    """
    Dashboard widgets for displaying key metrics
    """
    WIDGET_TYPES = [
        ('chart', _('مخطط')),
        ('counter', _('عداد')),
        ('table', _('جدول')),
        ('gauge', _('مقياس')),
        ('progress', _('شريط تقدم')),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الودجت')
    )

    widget_type = models.CharField(
        max_length=20,
        choices=WIDGET_TYPES,
        verbose_name=_('نوع الودجت')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('العنوان')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('الوصف')
    )

    # Widget configuration stored as JSON
    configuration = models.JSONField(
        default=dict,
        verbose_name=_('إعدادات الودجت')
    )

    # Data source query
    query = models.TextField(
        verbose_name=_('استعلام البيانات')
    )

    # Display settings
    width = models.IntegerField(
        default=6,
        verbose_name=_('العرض (1-12)')
    )

    height = models.IntegerField(
        default=300,
        verbose_name=_('الارتفاع (بكسل)')
    )

    order = models.IntegerField(
        default=0,
        verbose_name=_('الترتيب')
    )

    # Refresh settings
    refresh_interval = models.IntegerField(
        default=300,  # 5 minutes
        verbose_name=_('فترة التحديث (ثواني)')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        verbose_name=_('أنشئ بواسطة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('ودجت لوحة التحكم')
        verbose_name_plural = _('ودجتات لوحة التحكم')
        ordering = ['order', 'name']

    def __str__(self):
        return self.title
