import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  User,
  Medicine,
  Customer,
  Sale,
  Supplier,
  PurchaseOrder,
  Report,
  DashboardStats,
  ApiResponse,
  LoginRequest,
  LoginResponse,
  PaginationParams,
  FilterParams,
} from '../types';

// إعداد Axios
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // إضافة interceptor للتوكن
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // إضافة interceptor للاستجابة
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // محاولة تجديد التوكن
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            try {
              const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
                refresh: refreshToken,
              });
              const newToken = response.data.access;
              localStorage.setItem('access_token', newToken);
              
              // إعادة المحاولة مع التوكن الجديد
              error.config.headers.Authorization = `Bearer ${newToken}`;
              return this.api.request(error.config);
            } catch (refreshError) {
              // فشل في تجديد التوكن، توجيه للتسجيل
              localStorage.removeItem('access_token');
              localStorage.removeItem('refresh_token');
              window.location.href = '/login';
            }
          } else {
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // المصادقة
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post('/auth/login/', credentials);
    return response.data;
  }

  async logout(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (refreshToken) {
      await this.api.post('/auth/logout/', { refresh: refreshToken });
    }
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  // لوحة التحكم
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.api.get('/dashboard/stats/');
    return response.data;
  }

  // الأدوية
  async getMedicines(params?: PaginationParams & FilterParams): Promise<ApiResponse<Medicine>> {
    const response = await this.api.get('/medicines/', { params });
    return response.data;
  }

  async getMedicine(id: number): Promise<Medicine> {
    const response = await this.api.get(`/medicines/${id}/`);
    return response.data;
  }

  async createMedicine(data: Partial<Medicine>): Promise<Medicine> {
    const response = await this.api.post('/medicines/', data);
    return response.data;
  }

  async updateMedicine(id: number, data: Partial<Medicine>): Promise<Medicine> {
    const response = await this.api.put(`/medicines/${id}/`, data);
    return response.data;
  }

  async deleteMedicine(id: number): Promise<void> {
    await this.api.delete(`/medicines/${id}/`);
  }

  // العملاء
  async getCustomers(params?: PaginationParams & FilterParams): Promise<ApiResponse<Customer>> {
    const response = await this.api.get('/customers/', { params });
    return response.data;
  }

  async getCustomer(id: number): Promise<Customer> {
    const response = await this.api.get(`/customers/${id}/`);
    return response.data;
  }

  async createCustomer(data: Partial<Customer>): Promise<Customer> {
    const response = await this.api.post('/customers/', data);
    return response.data;
  }

  async updateCustomer(id: number, data: Partial<Customer>): Promise<Customer> {
    const response = await this.api.put(`/customers/${id}/`, data);
    return response.data;
  }

  async deleteCustomer(id: number): Promise<void> {
    await this.api.delete(`/customers/${id}/`);
  }

  // المبيعات
  async getSales(params?: PaginationParams & FilterParams): Promise<ApiResponse<Sale>> {
    const response = await this.api.get('/sales/', { params });
    return response.data;
  }

  async getSale(id: number): Promise<Sale> {
    const response = await this.api.get(`/sales/${id}/`);
    return response.data;
  }

  async createSale(data: Partial<Sale>): Promise<Sale> {
    const response = await this.api.post('/sales/', data);
    return response.data;
  }

  // الموردين
  async getSuppliers(params?: PaginationParams & FilterParams): Promise<ApiResponse<Supplier>> {
    const response = await this.api.get('/suppliers/', { params });
    return response.data;
  }

  async getSupplier(id: number): Promise<Supplier> {
    const response = await this.api.get(`/suppliers/${id}/`);
    return response.data;
  }

  async createSupplier(data: Partial<Supplier>): Promise<Supplier> {
    const response = await this.api.post('/suppliers/', data);
    return response.data;
  }

  async updateSupplier(id: number, data: Partial<Supplier>): Promise<Supplier> {
    const response = await this.api.put(`/suppliers/${id}/`, data);
    return response.data;
  }

  async deleteSupplier(id: number): Promise<void> {
    await this.api.delete(`/suppliers/${id}/`);
  }

  // أوامر الشراء
  async getPurchaseOrders(params?: PaginationParams & FilterParams): Promise<ApiResponse<PurchaseOrder>> {
    const response = await this.api.get('/purchase-orders/', { params });
    return response.data;
  }

  async getPurchaseOrder(id: number): Promise<PurchaseOrder> {
    const response = await this.api.get(`/purchase-orders/${id}/`);
    return response.data;
  }

  async createPurchaseOrder(data: Partial<PurchaseOrder>): Promise<PurchaseOrder> {
    const response = await this.api.post('/purchase-orders/', data);
    return response.data;
  }

  // التقارير
  async getReports(params?: PaginationParams & FilterParams): Promise<ApiResponse<Report>> {
    const response = await this.api.get('/reports/', { params });
    return response.data;
  }

  async generateReport(type: string, parameters: any): Promise<Report> {
    const response = await this.api.post('/reports/generate/', { type, parameters });
    return response.data;
  }

  async downloadReport(id: number): Promise<Blob> {
    const response = await this.api.get(`/reports/${id}/download/`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // البحث العام
  async search(query: string): Promise<any> {
    const response = await this.api.get('/search/', { params: { q: query } });
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
