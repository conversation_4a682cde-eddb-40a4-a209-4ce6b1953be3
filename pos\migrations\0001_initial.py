# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
        ('customers', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CashRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('opening_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='مبلغ الافتتاح')),
                ('closing_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='مبلغ الإغلاق')),
                ('expected_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المبلغ المتوقع')),
                ('difference_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='الفرق')),
                ('is_open', models.BooleanField(default=True, verbose_name='مفتوح')),
                ('opened_at', models.DateTimeField(auto_now_add=True, verbose_name='وقت الافتتاح')),
                ('closed_at', models.DateTimeField(blank=True, null=True, verbose_name='وقت الإغلاق')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_registers', to='branches.branch', verbose_name='الفرع')),
                ('cashier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='الكاشير')),
            ],
            options={
                'verbose_name': 'صندوق النقد',
                'verbose_name_plural': 'صناديق النقد',
                'ordering': ['-opened_at'],
            },
        ),
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sale_number', models.CharField(max_length=20, unique=True, verbose_name='رقم البيع')),
                ('subtotal', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المجموع الفرعي')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='مبلغ الخصم')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='نسبة الخصم')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المبلغ المدفوع')),
                ('change_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='مبلغ الباقي')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('card', 'بطاقة'), ('insurance', 'تأمين'), ('credit', 'آجل'), ('mixed', 'مختلط')], default='cash', max_length=20, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('returned', 'مرتجع')], default='pending', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('insurance_company', models.CharField(blank=True, max_length=100, verbose_name='شركة التأمين')),
                ('insurance_number', models.CharField(blank=True, max_length=50, verbose_name='رقم التأمين')),
                ('insurance_coverage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5, verbose_name='نسبة التغطية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales', to='branches.branch', verbose_name='الفرع')),
                ('cashier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales', to=settings.AUTH_USER_MODEL, verbose_name='الكاشير')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales', to='customers.customer', verbose_name='العميل')),
                ('prescription', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customers.prescription', verbose_name='الوصفة الطبية')),
            ],
            options={
                'verbose_name': 'بيع',
                'verbose_name_plural': 'المبيعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Return',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الإرجاع')),
                ('reason', models.CharField(choices=[('defective', 'معيب'), ('expired', 'منتهي الصلاحية'), ('wrong_item', 'صنف خاطئ'), ('customer_request', 'طلب العميل'), ('doctor_change', 'تغيير الطبيب'), ('other', 'أخرى')], max_length=20, verbose_name='سبب الإرجاع')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='مبلغ الإرجاع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرجاع')),
                ('processed_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
                ('original_sale', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='returns', to='pos.sale', verbose_name='البيع الأصلي')),
            ],
            options={
                'verbose_name': 'إرجاع',
                'verbose_name_plural': 'الإرجاعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('receipt_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الفاتورة')),
                ('is_printed', models.BooleanField(default=False, verbose_name='تم الطباعة')),
                ('print_count', models.IntegerField(default=0, verbose_name='عدد مرات الطباعة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('sale', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='receipt', to='pos.sale', verbose_name='البيع')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='السعر الإجمالي')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='مبلغ الخصم')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.batch', verbose_name='الدفعة')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='pos.sale', verbose_name='البيع')),
            ],
            options={
                'verbose_name': 'عنصر البيع',
                'verbose_name_plural': 'عناصر البيع',
            },
        ),
        migrations.CreateModel(
            name='ReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية المرتجعة')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='السعر الإجمالي')),
                ('return_transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='pos.return', verbose_name='الإرجاع')),
                ('original_sale_item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='pos.saleitem', verbose_name='عنصر البيع الأصلي')),
            ],
            options={
                'verbose_name': 'عنصر الإرجاع',
                'verbose_name_plural': 'عناصر الإرجاع',
            },
        ),
    ]
