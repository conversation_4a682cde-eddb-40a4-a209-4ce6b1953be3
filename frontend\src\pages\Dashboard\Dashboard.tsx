import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUp,
  People,
  Inventory,
  Warning,
  Receipt,
  AttachMoney,
} from '@mui/icons-material';
import { DashboardStats } from '../../types';
import apiService from '../../services/api';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  trend?: string;
}

function StatCard({ title, value, icon, color, trend }: StatCardProps) {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="textSecondary" gutterBottom variant="overline">
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            {trend && (
              <Typography variant="body2" sx={{ color: 'success.main', mt: 1 }}>
                {trend}
              </Typography>
            )}
          </Box>
          <Box
            sx={{
              backgroundColor: color,
              borderRadius: '50%',
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      const data = await apiService.getDashboardStats();
      setStats(data);
    } catch (err: any) {
      setError('فشل في تحميل بيانات لوحة التحكم');
      console.error('Dashboard stats error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        لوحة التحكم
      </Typography>
      
      <Grid container spacing={3}>
        {/* إحصائيات سريعة */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="مبيعات اليوم"
            value={`${stats?.total_sales_today || 0} ر.س`}
            icon={<AttachMoney sx={{ color: 'white' }} />}
            color="success.main"
            trend="+12% من الأمس"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي العملاء"
            value={stats?.total_customers || 0}
            icon={<People sx={{ color: 'white' }} />}
            color="info.main"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="أصناف منخفضة المخزون"
            value={stats?.low_stock_items || 0}
            icon={<Warning sx={{ color: 'white' }} />}
            color="warning.main"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="أصناف منتهية الصلاحية"
            value={stats?.expired_items || 0}
            icon={<Inventory sx={{ color: 'white' }} />}
            color="error.main"
          />
        </Grid>

        {/* الأدوية الأكثر مبيعاً */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              الأدوية الأكثر مبيعاً
            </Typography>
            <List>
              {stats?.top_selling_medicines?.slice(0, 5).map((medicine, index) => (
                <ListItem key={medicine.id} divider>
                  <ListItemText
                    primary={medicine.name}
                    secondary={medicine.generic_name}
                  />
                  <Chip
                    label={`#${index + 1}`}
                    color="primary"
                    size="small"
                  />
                </ListItem>
              )) || (
                <ListItem>
                  <ListItemText primary="لا توجد بيانات متاحة" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>

        {/* المبيعات الأخيرة */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              المبيعات الأخيرة
            </Typography>
            <List>
              {stats?.recent_sales?.slice(0, 5).map((sale) => (
                <ListItem key={sale.id} divider>
                  <ListItemText
                    primary={`فاتورة #${sale.sale_number}`}
                    secondary={`${sale.total} ر.س - ${new Date(sale.sale_date).toLocaleDateString('ar-SA')}`}
                  />
                  <Chip
                    label={sale.status === 'completed' ? 'مكتملة' : 'ملغاة'}
                    color={sale.status === 'completed' ? 'success' : 'error'}
                    size="small"
                  />
                </ListItem>
              )) || (
                <ListItem>
                  <ListItemText primary="لا توجد مبيعات حديثة" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>

        {/* الوصفات المعلقة */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              الوصفات المعلقة
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Receipt sx={{ fontSize: 40, color: 'warning.main' }} />
              <Box>
                <Typography variant="h4">
                  {stats?.pending_prescriptions || 0}
                </Typography>
                <Typography color="textSecondary">
                  وصفة في الانتظار
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* الإيرادات الشهرية */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              إيرادات هذا الشهر
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <TrendingUp sx={{ fontSize: 40, color: 'success.main' }} />
              <Box>
                <Typography variant="h4">
                  {stats?.monthly_revenue || 0} ر.س
                </Typography>
                <Typography color="textSecondary">
                  إجمالي الإيرادات
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
