<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في الخادم - نظام إدارة الصيدليات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            margin: 20px;
        }
        
        .error-icon {
            font-size: 8rem;
            color: #f5576c;
            margin-bottom: 30px;
        }
        
        .error-title {
            font-size: 3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }
        
        .error-subtitle {
            font-size: 1.5rem;
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .error-description {
            color: #6c757d;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
            margin: 5px;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 87, 108, 0.3);
            color: white;
        }
        
        .btn-retry {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
            margin: 5px;
        }
        
        .btn-retry:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .error-details {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #f5576c;
        }
        
        .error-details h6 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .error-details ul {
            text-align: right;
            color: #6c757d;
            margin-bottom: 0;
        }
        
        .error-details li {
            margin-bottom: 8px;
        }
        
        .contact-info {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .contact-info h6 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .contact-info p {
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        @media (max-width: 768px) {
            .error-container {
                padding: 40px 30px;
                margin: 10px;
            }
            
            .error-icon {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-subtitle {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="error-title">500</h1>
        <h2 class="error-subtitle">خطأ في الخادم</h2>
        
        <p class="error-description">
            عذراً، حدث خطأ غير متوقع في الخادم. نحن نعمل على حل هذه المشكلة.
            يرجى المحاولة مرة أخرى خلال بضع دقائق.
        </p>
        
        <div class="mb-3">
            <a href="javascript:location.reload()" class="btn-retry">
                <i class="fas fa-redo me-2"></i>
                إعادة المحاولة
            </a>
            <a href="/" class="btn-home">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
        
        <div class="error-details">
            <h6><i class="fas fa-info-circle me-2"></i>ما يمكنك فعله:</h6>
            <ul>
                <li>انتظر بضع دقائق ثم أعد المحاولة</li>
                <li>تحقق من اتصالك بالإنترنت</li>
                <li>امسح ذاكرة التخزين المؤقت للمتصفح</li>
                <li>جرب استخدام متصفح آخر</li>
                <li>تواصل مع الدعم الفني إذا استمرت المشكلة</li>
            </ul>
        </div>
        
        <div class="contact-info">
            <h6><i class="fas fa-headset me-2"></i>تحتاج مساعدة؟</h6>
            <p><i class="fas fa-envelope me-2"></i>البريد الإلكتروني: <EMAIL></p>
            <p><i class="fas fa-phone me-2"></i>الهاتف: ************</p>
            <p><i class="fas fa-clock me-2"></i>ساعات العمل: 24/7</p>
        </div>
    </div>
    
    <script>
        // إعادة تحميل الصفحة تلقائياً بعد 30 ثانية
        setTimeout(function() {
            if (confirm('هل تريد إعادة تحميل الصفحة تلقائياً؟')) {
                location.reload();
            }
        }, 30000);
        
        // تسجيل الخطأ (يمكن إرساله للخادم)
        console.error('Server Error 500 occurred at:', new Date().toISOString());
        
        // إرسال تقرير خطأ (اختياري)
        function sendErrorReport() {
            // يمكن إضافة كود لإرسال تقرير الخطأ
            console.log('Error report would be sent here');
        }
    </script>
</body>
</html>
