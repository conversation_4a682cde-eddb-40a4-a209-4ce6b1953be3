from django.urls import path
from . import views

app_name = 'pos'

urlpatterns = [
    # قائمة المبيعات
    path('', views.sale_list_view, name='sale_list'),
    path('sales/', views.sale_list_view, name='sale_list'),

    # إنشاء بيع جديد
    path('new-sale/', views.new_sale_view, name='new_sale'),
    path('advanced-pos/', views.advanced_pos_view, name='advanced_pos'),
    path('sale/create/', views.create_sale_view, name='create_sale'),

    # تفاصيل البيع
    path('sale/<int:sale_id>/', views.sale_detail_view, name='sale_detail'),

    # طباعة الفاتورة
    path('sale/<int:sale_id>/print/', views.print_receipt_view, name='print_receipt'),
    path('sale/<int:sale_id>/receipt/', views.receipt_view, name='receipt'),

    # إرجاع المبيعات (سيتم إضافتها لاحقاً)
    # path('returns/', views.return_list_view, name='return_list'),
    # path('sale/<int:sale_id>/return/', views.create_return_view, name='create_return'),
    # path('return/<int:return_id>/', views.return_detail_view, name='return_detail'),

    # إدارة صندوق النقد
    path('cash-register/', views.cash_register_view, name='cash_register'),
    path('cash-register/open/', views.open_cash_register_view, name='open_cash_register'),
    path('cash-register/close/', views.close_cash_register_view, name='close_cash_register'),

    # تقارير نقاط البيع (سيتم إضافتها لاحقاً)
    # path('reports/', views.pos_reports_view, name='pos_reports'),
    # path('reports/daily/', views.daily_sales_report_view, name='daily_sales_report'),
    # path('reports/cashier/', views.cashier_report_view, name='cashier_report'),

    # API endpoints
    path('api/search-medicine/', views.search_medicine_api, name='search_medicine_api'),
    path('api/get-medicine/<int:medicine_id>/', views.get_medicine_api, name='get_medicine_api'),
    path('api/search-products/', views.search_products_api, name='search_products_api'),
    path('api/search-customers/', views.search_customers_api, name='search_customers_api'),
    path('api/create-sale/', views.create_sale_api, name='create_sale_api'),
]
