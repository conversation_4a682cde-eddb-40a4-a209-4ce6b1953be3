/* استيراد خط Cairo للعربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

* {
  box-sizing: border-box;
}

html {
  direction: rtl;
}

body {
  margin: 0;
  font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  text-align: right;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* تخصيص Material-UI للعربية */
.MuiTextField-root .MuiInputBase-input {
  text-align: right;
}

.MuiTableCell-root {
  text-align: right;
}

.MuiTableCell-head {
  font-weight: 600;
}

/* تحسين عرض الأرقام */
.number {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* تحسين عرض التواريخ */
.date {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* تحسين الأزرار */
.MuiButton-root {
  font-family: 'Cairo', sans-serif;
}

/* تحسين القوائم */
.MuiMenuItem-root {
  text-align: right;
  justify-content: flex-end;
}

/* تحسين الجداول */
.MuiTablePagination-root {
  direction: rtl;
}

.MuiTablePagination-toolbar {
  direction: rtl;
}

.MuiTablePagination-selectLabel,
.MuiTablePagination-displayedRows {
  direction: rtl;
}

/* تحسين النماذج */
.MuiFormControl-root {
  direction: rtl;
}

.MuiInputLabel-root {
  right: 14px;
  left: auto;
  transform-origin: right;
}

.MuiInputLabel-shrink {
  transform: translate(0, -9px) scale(0.75);
}

/* تحسين الحقول */
.MuiOutlinedInput-root {
  direction: rtl;
}

.MuiOutlinedInput-input {
  padding-right: 14px;
  padding-left: 14px;
}

/* تحسين الأيقونات */
.MuiListItemIcon-root {
  min-width: 40px;
  margin-left: 16px;
  margin-right: 0;
}

/* تحسين الدرج الجانبي */
.MuiDrawer-paper {
  direction: rtl;
}

/* تحسين شريط التطبيق */
.MuiAppBar-root {
  direction: rtl;
}

/* تحسين البطاقات */
.MuiCard-root {
  direction: rtl;
}

/* تحسين الحوارات */
.MuiDialog-paper {
  direction: rtl;
}

/* تحسين التبويبات */
.MuiTabs-root {
  direction: rtl;
}

.MuiTab-root {
  direction: rtl;
}
