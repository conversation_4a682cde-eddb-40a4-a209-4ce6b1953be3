# Generated by Django 5.2.1 on 2025-05-26 14:45

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("branches", "0001_initial"),
        ("customers", "0002_initial"),
        ("inventory", "0002_initial"),
        ("pos", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["sale_number"], name="pos_sale_sale_nu_ec47fa_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["status", "created_at"], name="pos_sale_status_506c61_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["cashier", "created_at"], name="pos_sale_cashier_66b2b2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["customer", "created_at"], name="pos_sale_custome_5bd2ad_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["branch", "created_at"], name="pos_sale_branch__c390a6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["payment_method", "created_at"],
                name="pos_sale_payment_74b6f8_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["created_at", "total_amount"],
                name="pos_sale_created_c48b3e_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["status", "payment_method"], name="pos_sale_status_5c5fdd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="sale",
            index=models.Index(
                fields=["completed_at"], name="pos_sale_complet_4ab3b9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="saleitem",
            index=models.Index(
                fields=["sale", "batch"], name="pos_saleite_sale_id_43fab6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="saleitem",
            index=models.Index(
                fields=["batch", "created_at"], name="pos_saleite_batch_i_c3c9a6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="saleitem",
            index=models.Index(
                fields=["created_at"], name="pos_saleite_created_a0da73_idx"
            ),
        ),
    ]
