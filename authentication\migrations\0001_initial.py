# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.contrib.auth.models
import django.contrib.auth.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('role', models.CharField(choices=[('admin', 'مدير النظام'), ('manager', 'مدير الصيدلية'), ('pharmacist', 'صيدلي'), ('cashier', 'كاشير'), ('inventory_manager', 'مدير المخزون'), ('accountant', 'محاسب')], default='cashier', max_length=20, verbose_name='الدور')),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف')),
                ('employee_id', models.CharField(blank=True, max_length=20, null=True, unique=True, verbose_name='رقم الموظف')),
                ('is_active_session', models.BooleanField(default=False, verbose_name='جلسة نشطة')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='آخر نشاط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
                'ordering': ['-created_at'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الصلاحية')),
                ('codename', models.CharField(max_length=100, unique=True, verbose_name='الرمز')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('module', models.CharField(max_length=50, verbose_name='الوحدة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'صلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'ordering': ['module', 'name'],
            },
        ),
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', 'مدير النظام'), ('manager', 'مدير الصيدلية'), ('pharmacist', 'صيدلي'), ('cashier', 'كاشير'), ('inventory_manager', 'مدير المخزون'), ('accountant', 'محاسب')], max_length=20, verbose_name='الدور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'صلاحية الدور',
                'verbose_name_plural': 'صلاحيات الأدوار',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('login_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت تسجيل الدخول')),
                ('logout_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت تسجيل الخروج')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'جلسة مستخدم',
                'verbose_name_plural': 'جلسات المستخدمين',
                'ordering': ['-login_time'],
            },
        ),
    ]
