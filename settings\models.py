from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
import json

User = get_user_model()


class SystemSetting(models.Model):
    """
    System-wide settings and configurations
    """
    SETTING_TYPES = [
        ('string', _('نص')),
        ('integer', _('رقم صحيح')),
        ('decimal', _('رقم عشري')),
        ('boolean', _('منطقي')),
        ('json', _('JSON')),
        ('file', _('ملف')),
    ]

    key = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('المفتاح')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('الاسم')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('الوصف')
    )

    setting_type = models.CharField(
        max_length=20,
        choices=SETTING_TYPES,
        default='string',
        verbose_name=_('نوع الإعداد')
    )

    value = models.TextField(
        blank=True,
        verbose_name=_('القيمة')
    )

    default_value = models.TextField(
        blank=True,
        verbose_name=_('القيمة الافتراضية')
    )

    category = models.CharField(
        max_length=50,
        verbose_name=_('الفئة')
    )

    is_editable = models.BooleanField(
        default=True,
        verbose_name=_('قابل للتعديل')
    )

    is_sensitive = models.BooleanField(
        default=False,
        verbose_name=_('حساس')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('تم التحديث بواسطة')
    )

    class Meta:
        verbose_name = _('إعداد النظام')
        verbose_name_plural = _('إعدادات النظام')
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.category} - {self.name}"

    def get_value(self):
        """Get the parsed value based on setting type"""
        if not self.value:
            return self.get_default_value()

        if self.setting_type == 'integer':
            return int(self.value)
        elif self.setting_type == 'decimal':
            return float(self.value)
        elif self.setting_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.setting_type == 'json':
            return json.loads(self.value)
        else:
            return self.value

    def get_default_value(self):
        """Get the parsed default value"""
        if not self.default_value:
            return None

        if self.setting_type == 'integer':
            return int(self.default_value)
        elif self.setting_type == 'decimal':
            return float(self.default_value)
        elif self.setting_type == 'boolean':
            return self.default_value.lower() in ('true', '1', 'yes', 'on')
        elif self.setting_type == 'json':
            return json.loads(self.default_value)
        else:
            return self.default_value

    def set_value(self, value):
        """Set the value with proper type conversion"""
        if self.setting_type == 'json':
            self.value = json.dumps(value)
        else:
            self.value = str(value)


class BackupConfiguration(models.Model):
    """
    Backup configuration settings
    """
    BACKUP_TYPES = [
        ('full', _('نسخة كاملة')),
        ('incremental', _('نسخة تزايدية')),
        ('differential', _('نسخة تفاضلية')),
    ]

    STORAGE_TYPES = [
        ('local', _('محلي')),
        ('ftp', 'FTP'),
        ('sftp', 'SFTP'),
        ('cloud', _('سحابي')),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم الإعداد')
    )

    backup_type = models.CharField(
        max_length=20,
        choices=BACKUP_TYPES,
        default='full',
        verbose_name=_('نوع النسخة')
    )

    storage_type = models.CharField(
        max_length=20,
        choices=STORAGE_TYPES,
        default='local',
        verbose_name=_('نوع التخزين')
    )

    # Scheduling
    is_scheduled = models.BooleanField(
        default=False,
        verbose_name=_('مجدول')
    )

    schedule_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', _('يومي')),
            ('weekly', _('أسبوعي')),
            ('monthly', _('شهري')),
        ],
        blank=True,
        verbose_name=_('تكرار الجدولة')
    )

    schedule_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت الجدولة')
    )

    # Storage settings
    storage_path = models.CharField(
        max_length=500,
        verbose_name=_('مسار التخزين')
    )

    max_backups = models.IntegerField(
        default=7,
        validators=[MinValueValidator(1)],
        verbose_name=_('الحد الأقصى للنسخ')
    )

    # Compression
    compress_backup = models.BooleanField(
        default=True,
        verbose_name=_('ضغط النسخة')
    )

    compression_level = models.IntegerField(
        default=6,
        validators=[MinValueValidator(1), MaxValueValidator(9)],
        verbose_name=_('مستوى الضغط')
    )

    # Encryption
    encrypt_backup = models.BooleanField(
        default=False,
        verbose_name=_('تشفير النسخة')
    )

    encryption_key = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('مفتاح التشفير')
    )

    # Notification
    notify_on_success = models.BooleanField(
        default=True,
        verbose_name=_('إشعار عند النجاح')
    )

    notify_on_failure = models.BooleanField(
        default=True,
        verbose_name=_('إشعار عند الفشل')
    )

    notification_emails = models.TextField(
        blank=True,
        verbose_name=_('بريد الإشعارات')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('إعداد النسخ الاحتياطي')
        verbose_name_plural = _('إعدادات النسخ الاحتياطي')

    def __str__(self):
        return self.name


class BackupHistory(models.Model):
    """
    Backup execution history
    """
    STATUS_CHOICES = [
        ('running', _('قيد التشغيل')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
        ('cancelled', _('ملغي')),
    ]

    configuration = models.ForeignKey(
        BackupConfiguration,
        on_delete=models.CASCADE,
        related_name='history',
        verbose_name=_('الإعداد')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='running',
        verbose_name=_('الحالة')
    )

    start_time = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت البداية')
    )

    end_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت النهاية')
    )

    file_path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name=_('مسار الملف')
    )

    file_size = models.BigIntegerField(
        null=True,
        blank=True,
        verbose_name=_('حجم الملف')
    )

    error_message = models.TextField(
        blank=True,
        verbose_name=_('رسالة الخطأ')
    )

    triggered_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('تم بواسطة')
    )

    class Meta:
        verbose_name = _('سجل النسخ الاحتياطي')
        verbose_name_plural = _('سجلات النسخ الاحتياطي')
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.configuration.name} - {self.start_time.date()}"

    @property
    def duration(self):
        """Calculate backup duration"""
        if self.end_time:
            return self.end_time - self.start_time
        return None


class AuditLog(models.Model):
    """
    System audit log for tracking user actions
    """
    ACTION_TYPES = [
        ('create', _('إنشاء')),
        ('update', _('تحديث')),
        ('delete', _('حذف')),
        ('login', _('تسجيل دخول')),
        ('logout', _('تسجيل خروج')),
        ('view', _('عرض')),
        ('export', _('تصدير')),
        ('import', _('استيراد')),
        ('backup', _('نسخ احتياطي')),
        ('restore', _('استعادة')),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('المستخدم')
    )

    action = models.CharField(
        max_length=20,
        choices=ACTION_TYPES,
        verbose_name=_('الإجراء')
    )

    model_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('اسم النموذج')
    )

    object_id = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('معرف الكائن')
    )

    object_repr = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('تمثيل الكائن')
    )

    changes = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('التغييرات')
    )

    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('عنوان IP')
    )

    user_agent = models.TextField(
        blank=True,
        verbose_name=_('معلومات المتصفح')
    )

    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('الوقت')
    )

    class Meta:
        verbose_name = _('سجل المراجعة')
        verbose_name_plural = _('سجلات المراجعة')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['model_name', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.user} - {self.get_action_display()} - {self.timestamp}"


class NotificationTemplate(models.Model):
    """
    Templates for system notifications
    """
    NOTIFICATION_TYPES = [
        ('email', _('بريد إلكتروني')),
        ('sms', _('رسالة نصية')),
        ('push', _('إشعار فوري')),
        ('system', _('إشعار النظام')),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_('اسم القالب')
    )

    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        verbose_name=_('نوع الإشعار')
    )

    subject = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_('الموضوع')
    )

    body = models.TextField(
        verbose_name=_('المحتوى')
    )

    # Template variables (JSON)
    variables = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('المتغيرات')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('قالب إشعار')
        verbose_name_plural = _('قوالب الإشعارات')
        unique_together = ['name', 'notification_type']

    def __str__(self):
        return f"{self.name} ({self.get_notification_type_display()})"
