from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)

from . import views

# إنشاء router للـ ViewSets
router = DefaultRouter()
router.register(r'medicines', views.MedicineViewSet)
router.register(r'customers', views.CustomerViewSet)
router.register(r'suppliers', views.SupplierViewSet)
router.register(r'sales', views.SaleViewSet)
router.register(r'purchase-orders', views.PurchaseOrderViewSet)
router.register(r'reports', views.ReportViewSet)

urlpatterns = [
    # Authentication endpoints
    path('auth/login/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('auth/logout/', views.LogoutView.as_view(), name='logout'),
    
    # Dashboard
    path('dashboard/stats/', views.DashboardStatsView.as_view(), name='dashboard_stats'),
    
    # Search
    path('search/', views.SearchView.as_view(), name='search'),
    
    # Include router URLs
    path('', include(router.urls)),
]
