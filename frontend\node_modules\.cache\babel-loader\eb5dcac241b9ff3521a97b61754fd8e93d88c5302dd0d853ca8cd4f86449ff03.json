{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { DEFAULT_STEP_NAVIGATION } from \"../utils/createStepNavigation.js\";\nlet warnedOnceNotValidView = false;\nexport function useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange,\n  getStepNavigation\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI X: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI X: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = useControlled({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = useControlled({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  const stepNavigation = getStepNavigation ? getStepNavigation({\n    setView,\n    view,\n    defaultView: defaultView.current,\n    views\n  }) : DEFAULT_STEP_NAVIGATION;\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = views[viewIndex - 1] ?? null;\n  const nextView = views[viewIndex + 1] ?? null;\n  const handleFocusedViewChange = useEventCallback((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange?.(viewToFocus, hasFocus);\n  });\n  const handleChangeView = useEventCallback(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = useEventCallback(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = useEventCallback((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but when it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n\n    // The selected view can be different from the active view,\n    // This can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    let currentView = null;\n    if (selectedView != null && selectedView !== view) {\n      currentView = selectedView;\n    } else if (isSelectionFinishedOnCurrentView) {\n      currentView = view;\n    }\n    if (currentView == null) {\n      return;\n    }\n    const viewToNavigateTo = views[views.indexOf(currentView) + 1];\n    if (viewToNavigateTo == null || !stepNavigation.areViewsInSameStep(currentView, viewToNavigateTo)) {\n      return;\n    }\n    handleChangeView(viewToNavigateTo);\n  });\n  return _extends({}, stepNavigation, {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up-to-date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useControlled", "DEFAULT_STEP_NAVIGATION", "warnedOnceNotValidView", "useViews", "onChange", "onViewChange", "openTo", "view", "inView", "views", "autoFocus", "focused<PERSON>iew", "inFocusedView", "onFocusedViewChange", "getStepNavigation", "process", "env", "NODE_ENV", "includes", "console", "warn", "join", "previousOpenTo", "useRef", "previousViews", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "name", "state", "controlled", "default", "current", "defaultFocusedView", "setFocusedView", "stepNavigation", "useEffect", "some", "previousView", "viewIndex", "indexOf", "next<PERSON>iew", "handleFocusedViewChange", "viewToFocus", "hasFocus", "prevFocusedView", "handleChangeView", "newView", "goToNextView", "setValueAndGoToNextView", "value", "currentViewSelectionState", "<PERSON><PERSON><PERSON><PERSON>", "isSelectionFinishedOnCurrentView", "hasMoreViews", "length", "Boolean", "globalSelectionState", "current<PERSON>iew", "viewToNavigateTo", "areViewsInSameStep"], "sources": ["D:/pos/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useViews.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { DEFAULT_STEP_NAVIGATION } from \"../utils/createStepNavigation.js\";\nlet warnedOnceNotValidView = false;\nexport function useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange,\n  getStepNavigation\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI X: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI X: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = useControlled({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = useControlled({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  const stepNavigation = getStepNavigation ? getStepNavigation({\n    setView,\n    view,\n    defaultView: defaultView.current,\n    views\n  }) : DEFAULT_STEP_NAVIGATION;\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = views[viewIndex - 1] ?? null;\n  const nextView = views[viewIndex + 1] ?? null;\n  const handleFocusedViewChange = useEventCallback((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange?.(viewToFocus, hasFocus);\n  });\n  const handleChangeView = useEventCallback(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = useEventCallback(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = useEventCallback((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but when it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n\n    // The selected view can be different from the active view,\n    // This can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    let currentView = null;\n    if (selectedView != null && selectedView !== view) {\n      currentView = selectedView;\n    } else if (isSelectionFinishedOnCurrentView) {\n      currentView = view;\n    }\n    if (currentView == null) {\n      return;\n    }\n    const viewToNavigateTo = views[views.indexOf(currentView) + 1];\n    if (viewToNavigateTo == null || !stepNavigation.areViewsInSameStep(currentView, viewToNavigateTo)) {\n      return;\n    }\n    handleChangeView(viewToNavigateTo);\n  });\n  return _extends({}, stepNavigation, {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up-to-date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,IAAIC,sBAAsB,GAAG,KAAK;AAClC,OAAO,SAASC,QAAQA,CAAC;EACvBC,QAAQ;EACRC,YAAY;EACZC,MAAM;EACNC,IAAI,EAAEC,MAAM;EACZC,KAAK;EACLC,SAAS;EACTC,WAAW,EAAEC,aAAa;EAC1BC,mBAAmB;EACnBC;AACF,CAAC,EAAE;EACD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACf,sBAAsB,EAAE;MAC3B,IAAIM,MAAM,IAAI,IAAI,IAAI,CAACC,KAAK,CAACS,QAAQ,CAACV,MAAM,CAAC,EAAE;QAC7CW,OAAO,CAACC,IAAI,CAAC,kBAAkBZ,MAAM,0BAA0B,EAAE,sCAAsCC,KAAK,CAACY,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACjInB,sBAAsB,GAAG,IAAI;MAC/B;MACA,IAAIM,MAAM,IAAI,IAAI,IAAIF,MAAM,IAAI,IAAI,IAAI,CAACG,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,EAAE;QAC/Da,OAAO,CAACC,IAAI,CAAC,oBAAoBd,MAAM,0BAA0B,EAAE,sCAAsCG,KAAK,CAACY,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnInB,sBAAsB,GAAG,IAAI;MAC/B;IACF;EACF;EACA,MAAMoB,cAAc,GAAGxB,KAAK,CAACyB,MAAM,CAACjB,MAAM,CAAC;EAC3C,MAAMkB,aAAa,GAAG1B,KAAK,CAACyB,MAAM,CAACd,KAAK,CAAC;EACzC,MAAMgB,WAAW,GAAG3B,KAAK,CAACyB,MAAM,CAACd,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5E,MAAM,CAACF,IAAI,EAAEmB,OAAO,CAAC,GAAG1B,aAAa,CAAC;IACpC2B,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAErB,MAAM;IAClBsB,OAAO,EAAEL,WAAW,CAACM;EACvB,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGlC,KAAK,CAACyB,MAAM,CAACb,SAAS,GAAGH,IAAI,GAAG,IAAI,CAAC;EAChE,MAAM,CAACI,WAAW,EAAEsB,cAAc,CAAC,GAAGjC,aAAa,CAAC;IAClD2B,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAEjB,aAAa;IACzBkB,OAAO,EAAEE,kBAAkB,CAACD;EAC9B,CAAC,CAAC;EACF,MAAMG,cAAc,GAAGpB,iBAAiB,GAAGA,iBAAiB,CAAC;IAC3DY,OAAO;IACPnB,IAAI;IACJkB,WAAW,EAAEA,WAAW,CAACM,OAAO;IAChCtB;EACF,CAAC,CAAC,GAAGR,uBAAuB;EAC5BH,KAAK,CAACqC,SAAS,CAAC,MAAM;IACpB;IACA,IAAIb,cAAc,CAACS,OAAO,IAAIT,cAAc,CAACS,OAAO,KAAKzB,MAAM,IAAIkB,aAAa,CAACO,OAAO,IAAIP,aAAa,CAACO,OAAO,CAACK,IAAI,CAACC,YAAY,IAAI,CAAC5B,KAAK,CAACS,QAAQ,CAACmB,YAAY,CAAC,CAAC,EAAE;MACrKX,OAAO,CAACjB,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;MACnDe,aAAa,CAACO,OAAO,GAAGtB,KAAK;MAC7Ba,cAAc,CAACS,OAAO,GAAGzB,MAAM;IACjC;EACF,CAAC,EAAE,CAACA,MAAM,EAAEoB,OAAO,EAAEnB,IAAI,EAAEE,KAAK,CAAC,CAAC;EAClC,MAAM6B,SAAS,GAAG7B,KAAK,CAAC8B,OAAO,CAAChC,IAAI,CAAC;EACrC,MAAM8B,YAAY,GAAG5B,KAAK,CAAC6B,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI;EACjD,MAAME,QAAQ,GAAG/B,KAAK,CAAC6B,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI;EAC7C,MAAMG,uBAAuB,GAAG1C,gBAAgB,CAAC,CAAC2C,WAAW,EAAEC,QAAQ,KAAK;IAC1E,IAAIA,QAAQ,EAAE;MACZ;MACAV,cAAc,CAACS,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACAT,cAAc,CAACW,eAAe,IAAIF,WAAW,KAAKE,eAAe,GAAG,IAAI,GAAGA,eAAe,CAAC;MAC3F,CAAC;IACH;IACA/B,mBAAmB,GAAG6B,WAAW,EAAEC,QAAQ,CAAC;EAC9C,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG9C,gBAAgB,CAAC+C,OAAO,IAAI;IACnD;IACAL,uBAAuB,CAACK,OAAO,EAAE,IAAI,CAAC;IACtC,IAAIA,OAAO,KAAKvC,IAAI,EAAE;MACpB;IACF;IACAmB,OAAO,CAACoB,OAAO,CAAC;IAChB,IAAIzC,YAAY,EAAE;MAChBA,YAAY,CAACyC,OAAO,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGhD,gBAAgB,CAAC,MAAM;IAC1C,IAAIyC,QAAQ,EAAE;MACZK,gBAAgB,CAACL,QAAQ,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,MAAMQ,uBAAuB,GAAGjD,gBAAgB,CAAC,CAACkD,KAAK,EAAEC,yBAAyB,EAAEC,YAAY,KAAK;IACnG,MAAMC,gCAAgC,GAAGF,yBAAyB,KAAK,QAAQ;IAC/E,MAAMG,YAAY,GAAGF,YAAY;IACjC;IACA;IACA1C,KAAK,CAAC8B,OAAO,CAACY,YAAY,CAAC,GAAG1C,KAAK,CAAC6C,MAAM,GAAG,CAAC,GAAGC,OAAO,CAACf,QAAQ,CAAC;IAClE,MAAMgB,oBAAoB,GAAGJ,gCAAgC,IAAIC,YAAY,GAAG,SAAS,GAAGH,yBAAyB;IACrH9C,QAAQ,CAAC6C,KAAK,EAAEO,oBAAoB,EAAEL,YAAY,CAAC;;IAEnD;IACA;IACA,IAAIM,WAAW,GAAG,IAAI;IACtB,IAAIN,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK5C,IAAI,EAAE;MACjDkD,WAAW,GAAGN,YAAY;IAC5B,CAAC,MAAM,IAAIC,gCAAgC,EAAE;MAC3CK,WAAW,GAAGlD,IAAI;IACpB;IACA,IAAIkD,WAAW,IAAI,IAAI,EAAE;MACvB;IACF;IACA,MAAMC,gBAAgB,GAAGjD,KAAK,CAACA,KAAK,CAAC8B,OAAO,CAACkB,WAAW,CAAC,GAAG,CAAC,CAAC;IAC9D,IAAIC,gBAAgB,IAAI,IAAI,IAAI,CAACxB,cAAc,CAACyB,kBAAkB,CAACF,WAAW,EAAEC,gBAAgB,CAAC,EAAE;MACjG;IACF;IACAb,gBAAgB,CAACa,gBAAgB,CAAC;EACpC,CAAC,CAAC;EACF,OAAO7D,QAAQ,CAAC,CAAC,CAAC,EAAEqC,cAAc,EAAE;IAClC3B,IAAI;IACJmB,OAAO,EAAEmB,gBAAgB;IACzBlC,WAAW;IACXsB,cAAc,EAAEQ,uBAAuB;IACvCD,QAAQ;IACRH,YAAY;IACZ;IACAZ,WAAW,EAAEhB,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC;IACvDsC,YAAY;IACZC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}