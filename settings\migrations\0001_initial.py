# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BackupConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الإعداد')),
                ('backup_type', models.CharField(choices=[('full', 'نسخة كاملة'), ('incremental', 'نسخة تزايدية'), ('differential', 'نسخة تفاضلية')], default='full', max_length=20, verbose_name='نوع النسخة')),
                ('storage_type', models.CharField(choices=[('local', 'محلي'), ('ftp', 'FTP'), ('sftp', 'SFTP'), ('cloud', 'سحابي')], default='local', max_length=20, verbose_name='نوع التخزين')),
                ('is_scheduled', models.BooleanField(default=False, verbose_name='مجدول')),
                ('schedule_frequency', models.CharField(blank=True, choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], max_length=20, verbose_name='تكرار الجدولة')),
                ('schedule_time', models.TimeField(blank=True, null=True, verbose_name='وقت الجدولة')),
                ('storage_path', models.CharField(max_length=500, verbose_name='مسار التخزين')),
                ('max_backups', models.IntegerField(default=7, validators=[django.core.validators.MinValueValidator(1)], verbose_name='الحد الأقصى للنسخ')),
                ('compress_backup', models.BooleanField(default=True, verbose_name='ضغط النسخة')),
                ('compression_level', models.IntegerField(default=6, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(9)], verbose_name='مستوى الضغط')),
                ('encrypt_backup', models.BooleanField(default=False, verbose_name='تشفير النسخة')),
                ('encryption_key', models.CharField(blank=True, max_length=500, verbose_name='مفتاح التشفير')),
                ('notify_on_success', models.BooleanField(default=True, verbose_name='إشعار عند النجاح')),
                ('notify_on_failure', models.BooleanField(default=True, verbose_name='إشعار عند الفشل')),
                ('notification_emails', models.TextField(blank=True, verbose_name='بريد الإشعارات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد النسخ الاحتياطي',
                'verbose_name_plural': 'إعدادات النسخ الاحتياطي',
            },
        ),
        migrations.CreateModel(
            name='BackupHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('running', 'قيد التشغيل'), ('completed', 'مكتمل'), ('failed', 'فشل'), ('cancelled', 'ملغي')], default='running', max_length=20, verbose_name='الحالة')),
                ('start_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت البداية')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت النهاية')),
                ('file_path', models.CharField(blank=True, max_length=500, verbose_name='مسار الملف')),
                ('file_size', models.BigIntegerField(blank=True, null=True, verbose_name='حجم الملف')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('configuration', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='settings.backupconfiguration', verbose_name='الإعداد')),
                ('triggered_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
            ],
            options={
                'verbose_name': 'سجل النسخ الاحتياطي',
                'verbose_name_plural': 'سجلات النسخ الاحتياطي',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('notification_type', models.CharField(choices=[('email', 'بريد إلكتروني'), ('sms', 'رسالة نصية'), ('push', 'إشعار فوري'), ('system', 'إشعار النظام')], max_length=20, verbose_name='نوع الإشعار')),
                ('subject', models.CharField(blank=True, max_length=200, verbose_name='الموضوع')),
                ('body', models.TextField(verbose_name='المحتوى')),
                ('variables', models.JSONField(blank=True, default=list, verbose_name='المتغيرات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قالب إشعار',
                'verbose_name_plural': 'قوالب الإشعارات',
                'unique_together': {('name', 'notification_type')},
            },
        ),
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='المفتاح')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('setting_type', models.CharField(choices=[('string', 'نص'), ('integer', 'رقم صحيح'), ('decimal', 'رقم عشري'), ('boolean', 'منطقي'), ('json', 'JSON'), ('file', 'ملف')], default='string', max_length=20, verbose_name='نوع الإعداد')),
                ('value', models.TextField(blank=True, verbose_name='القيمة')),
                ('default_value', models.TextField(blank=True, verbose_name='القيمة الافتراضية')),
                ('category', models.CharField(max_length=50, verbose_name='الفئة')),
                ('is_editable', models.BooleanField(default=True, verbose_name='قابل للتعديل')),
                ('is_sensitive', models.BooleanField(default=False, verbose_name='حساس')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'إعداد النظام',
                'verbose_name_plural': 'إعدادات النظام',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('view', 'عرض'), ('export', 'تصدير'), ('import', 'استيراد'), ('backup', 'نسخ احتياطي'), ('restore', 'استعادة')], max_length=20, verbose_name='الإجراء')),
                ('model_name', models.CharField(blank=True, max_length=100, verbose_name='اسم النموذج')),
                ('object_id', models.CharField(blank=True, max_length=100, verbose_name='معرف الكائن')),
                ('object_repr', models.CharField(blank=True, max_length=200, verbose_name='تمثيل الكائن')),
                ('changes', models.JSONField(blank=True, default=dict, verbose_name='التغييرات')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='الوقت')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل المراجعة',
                'verbose_name_plural': 'سجلات المراجعة',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='settings_au_user_id_0a1372_idx'), models.Index(fields=['model_name', 'timestamp'], name='settings_au_model_n_4629cf_idx'), models.Index(fields=['action', 'timestamp'], name='settings_au_action_b2d33d_idx')],
            },
        ),
    ]
