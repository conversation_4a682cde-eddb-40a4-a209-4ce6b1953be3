{% load static %}

{% if panel.has_content and panel.enabled %}
  <div id="{{ panel.panel_id }}" class="djdt-panelContent djdt-hidden">
    <div class="djDebugPanelTitle">
      <button type="button" class="djDebugClose">×</button>
      <h3>{{ panel.title }}</h3>
    </div>
    <div class="djDebugPanelContent">
      {% if toolbar.should_render_panels %}
        {% for script in panel.scripts %}<script type="module" src="{{ script }}" async></script>{% endfor %}
        <div class="djdt-scroll">{{ panel.content }}</div>
      {% else %}
        <div class="djdt-loader"></div>
        <div class="djdt-scroll"></div>
      {% endif %}
    </div>
  </div>
{% endif %}
