from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import views as auth_views
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from django.http import JsonResponse

from pos.models import Sale, SaleItem
from inventory.models import Medicine, StockAlert, Batch
from customers.models import Customer
from suppliers.models import Supplier


class CustomLoginView(auth_views.LoginView):
    """عرض تسجيل الدخول المخصص"""
    template_name = 'registration/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return '/dashboard/'

    def form_valid(self, form):
        messages.success(self.request, f'مرحباً {form.get_user().get_full_name() or form.get_user().username}!')
        return super().form_valid(form)


@login_required
def dashboard_view(request):
    """لوحة التحكم الرئيسية"""

    # إحصائيات اليوم
    today = timezone.now().date()

    # مبيعات اليوم
    today_sales = Sale.objects.filter(
        created_at__date=today,
        status='completed'
    )

    stats = {
        'total_sales': today_sales.count(),
        'total_revenue': today_sales.aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'total_customers': Customer.objects.count(),
        'low_stock_items': StockAlert.objects.filter(
            alert_type='low_stock',
            is_acknowledged=False
        ).count(),
    }

    # آخر المبيعات
    recent_sales = Sale.objects.filter(
        status='completed'
    ).order_by('-created_at')[:5]

    # تنبيهات المخزون
    stock_alerts = StockAlert.objects.filter(
        is_acknowledged=False
    ).select_related('medicine')[:5]

    # الأدوية المنتهية الصلاحية
    expiring_batches = Batch.objects.filter(
        expiry_date__lte=timezone.now().date() + timedelta(days=30),
        current_quantity__gt=0
    ).select_related('medicine')[:5]

    # أكثر الأدوية مبيعاً
    top_selling = SaleItem.objects.filter(
        sale__created_at__date__gte=today - timedelta(days=30),
        sale__status='completed'
    ).values(
        'batch__medicine__name',
        'batch__medicine__generic_name'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum('total_price')
    ).order_by('-total_quantity')[:10]

    # بيانات الرسم البياني - آخر 7 أيام
    sales_chart_data = []
    sales_chart_labels = []

    for i in range(6, -1, -1):
        date = today - timedelta(days=i)
        daily_sales = Sale.objects.filter(
            created_at__date=date,
            status='completed'
        ).aggregate(total=Sum('total_amount'))['total'] or 0

        sales_chart_data.append(float(daily_sales))
        sales_chart_labels.append(date.strftime('%m/%d'))

    context = {
        'stats': stats,
        'recent_sales': recent_sales,
        'stock_alerts': stock_alerts,
        'expiring_batches': expiring_batches,
        'top_selling': top_selling,
        'sales_chart_data': sales_chart_data,
        'sales_chart_labels': sales_chart_labels,
    }

    return render(request, 'dashboard.html', context)


@login_required
def profile_view(request):
    """عرض الملف الشخصي"""
    if request.method == 'POST':
        user = request.user
        user.first_name = request.POST.get('first_name', '')
        user.last_name = request.POST.get('last_name', '')
        user.email = request.POST.get('email', '')
        user.save()

        messages.success(request, 'تم تحديث الملف الشخصي بنجاح!')
        return redirect('profile')

    return render(request, 'profile.html')


@login_required
def search_view(request):
    """البحث العام في النظام"""
    query = request.GET.get('q', '')
    results = {}

    if query:
        # البحث في الأدوية
        medicines = Medicine.objects.filter(
            Q(name__icontains=query) |
            Q(generic_name__icontains=query) |
            Q(barcode__icontains=query)
        )[:10]

        # البحث في العملاء
        customers = Customer.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(phone__icontains=query)
        )[:10]

        # البحث في الموردين
        suppliers = Supplier.objects.filter(
            Q(name__icontains=query) |
            Q(contact_person__icontains=query)
        )[:10]

        results = {
            'medicines': medicines,
            'customers': customers,
            'suppliers': suppliers,
            'query': query
        }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # إرجاع JSON للطلبات AJAX
        data = {
            'medicines': [
                {
                    'id': m.id,
                    'name': m.name,
                    'generic_name': m.generic_name,
                    'price': str(m.selling_price),
                    'stock': m.current_stock
                } for m in results.get('medicines', [])
            ],
            'customers': [
                {
                    'id': c.id,
                    'name': c.get_full_name(),
                    'phone': c.phone
                } for c in results.get('customers', [])
            ],
            'suppliers': [
                {
                    'id': s.id,
                    'name': s.name,
                    'contact': s.contact_person
                } for s in results.get('suppliers', [])
            ]
        }
        return JsonResponse(data)

    return render(request, 'search_results.html', results)


@login_required
def notifications_view(request):
    """عرض الإشعارات"""
    # تنبيهات المخزون
    stock_alerts = StockAlert.objects.filter(
        is_acknowledged=False
    ).select_related('medicine').order_by('-created_at')

    # الأدوية المنتهية الصلاحية
    expiring_batches = Batch.objects.filter(
        expiry_date__lte=timezone.now().date() + timedelta(days=30),
        current_quantity__gt=0
    ).select_related('medicine').order_by('expiry_date')

    context = {
        'stock_alerts': stock_alerts,
        'expiring_batches': expiring_batches,
    }

    return render(request, 'notifications.html', context)


def custom_404_view(request, exception):
    """صفحة خطأ 404 مخصصة"""
    return render(request, 'errors/404.html', status=404)


def custom_500_view(request):
    """صفحة خطأ 500 مخصصة"""
    return render(request, 'errors/500.html', status=500)


@login_required
def quick_stats_api(request):
    """API للإحصائيات السريعة"""
    today = timezone.now().date()

    # مبيعات اليوم
    today_sales = Sale.objects.filter(
        created_at__date=today,
        status='completed'
    )

    # مبيعات الأمس للمقارنة
    yesterday = today - timedelta(days=1)
    yesterday_sales = Sale.objects.filter(
        created_at__date=yesterday,
        status='completed'
    )

    today_revenue = today_sales.aggregate(total=Sum('total_amount'))['total'] or 0
    yesterday_revenue = yesterday_sales.aggregate(total=Sum('total_amount'))['total'] or 0

    # حساب النسبة المئوية للتغيير
    if yesterday_revenue > 0:
        revenue_change = ((today_revenue - yesterday_revenue) / yesterday_revenue) * 100
    else:
        revenue_change = 100 if today_revenue > 0 else 0

    data = {
        'today_sales_count': today_sales.count(),
        'today_revenue': float(today_revenue),
        'revenue_change': round(revenue_change, 1),
        'low_stock_count': StockAlert.objects.filter(
            alert_type='low_stock',
            is_acknowledged=False
        ).count(),
        'expiring_medicines': Batch.objects.filter(
            expiry_date__lte=timezone.now().date() + timedelta(days=30),
            current_quantity__gt=0
        ).count(),
    }

    return JsonResponse(data)
