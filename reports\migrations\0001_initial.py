# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardWidget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الودجت')),
                ('widget_type', models.CharField(choices=[('chart', 'مخطط'), ('counter', 'عداد'), ('table', 'جدول'), ('gauge', 'مقياس'), ('progress', 'شريط تقدم')], max_length=20, verbose_name='نوع الودجت')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('configuration', models.JSONField(default=dict, verbose_name='إعدادات الودجت')),
                ('query', models.TextField(verbose_name='استعلام البيانات')),
                ('width', models.IntegerField(default=6, verbose_name='العرض (1-12)')),
                ('height', models.IntegerField(default=300, verbose_name='الارتفاع (بكسل)')),
                ('order', models.IntegerField(default=0, verbose_name='الترتيب')),
                ('refresh_interval', models.IntegerField(default=300, verbose_name='فترة التحديث (ثواني)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'ودجت لوحة التحكم',
                'verbose_name_plural': 'ودجتات لوحة التحكم',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التقرير')),
                ('report_type', models.CharField(choices=[('sales', 'تقارير المبيعات'), ('inventory', 'تقارير المخزون'), ('financial', 'التقارير المالية'), ('customer', 'تقارير العملاء'), ('supplier', 'تقارير الموردين'), ('prescription', 'تقارير الوصفات'), ('audit', 'تقارير المراجعة')], max_length=20, verbose_name='نوع التقرير')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('configuration', models.JSONField(default=dict, verbose_name='إعدادات التقرير')),
                ('query', models.TextField(verbose_name='استعلام التقرير')),
                ('template_file', models.CharField(blank=True, max_length=200, verbose_name='ملف القالب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'قالب تقرير',
                'verbose_name_plural': 'قوالب التقارير',
                'ordering': ['report_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='GeneratedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم التقرير')),
                ('parameters', models.JSONField(default=dict, verbose_name='معاملات التقرير')),
                ('date_from', models.DateField(blank=True, null=True, verbose_name='من تاريخ')),
                ('date_to', models.DateField(blank=True, null=True, verbose_name='إلى تاريخ')),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('html', 'HTML')], default='pdf', max_length=10, verbose_name='التنسيق')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('processing', 'قيد المعالجة'), ('completed', 'مكتمل'), ('failed', 'فشل')], default='pending', max_length=20, verbose_name='الحالة')),
                ('file_path', models.CharField(blank=True, max_length=500, verbose_name='مسار الملف')),
                ('file_size', models.BigIntegerField(blank=True, null=True, verbose_name='حجم الملف')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('generated_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='branches.branch', verbose_name='الفرع')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='generated_reports', to='reports.reporttemplate', verbose_name='قالب التقرير')),
            ],
            options={
                'verbose_name': 'تقرير منشأ',
                'verbose_name_plural': 'التقارير المنشأة',
                'ordering': ['-generated_at'],
            },
        ),
        migrations.CreateModel(
            name='ScheduledReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الجدولة')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('yearly', 'سنوي')], max_length=20, verbose_name='التكرار')),
                ('parameters', models.JSONField(default=dict, verbose_name='معاملات التقرير')),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('html', 'HTML')], default='pdf', max_length=10, verbose_name='التنسيق')),
                ('email_recipients', models.TextField(help_text='عناوين البريد الإلكتروني مفصولة بفواصل', verbose_name='المستلمون')),
                ('email_subject', models.CharField(max_length=200, verbose_name='موضوع البريد')),
                ('email_body', models.TextField(blank=True, verbose_name='نص البريد')),
                ('next_run', models.DateTimeField(verbose_name='التشغيل التالي')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='branches.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scheduled_reports', to='reports.reporttemplate', verbose_name='قالب التقرير')),
            ],
            options={
                'verbose_name': 'تقرير مجدول',
                'verbose_name_plural': 'التقارير المجدولة',
                'ordering': ['next_run'],
            },
        ),
    ]
