import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  <PERSON>ert,
  Card,
  CardContent,
  CardActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import {
  Save,
  Backup,
  Restore,
  Security,
  Notifications,
  Business,
  Person,
  Delete,
  Add,
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function Settings() {
  const [tabValue, setTabValue] = useState(0);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  
  // إعدادات عامة
  const [generalSettings, setGeneralSettings] = useState({
    pharmacy_name: 'صيدلية الشفاء',
    pharmacy_license: 'PH-2024-001',
    address: 'الرياض، المملكة العربية السعودية',
    phone: '+966501234567',
    email: '<EMAIL>',
    tax_number: '*********',
    currency: 'SAR',
    language: 'ar',
  });

  // إعدادات النظام
  const [systemSettings, setSystemSettings] = useState({
    auto_backup: true,
    backup_frequency: 'daily',
    low_stock_alert: true,
    expiry_alert_days: 30,
    receipt_printer: true,
    barcode_scanner: true,
    loyalty_program: true,
    prescription_required_check: true,
  });

  // إعدادات الأمان
  const [securitySettings, setSecuritySettings] = useState({
    password_expiry_days: 90,
    max_login_attempts: 3,
    session_timeout: 30,
    two_factor_auth: false,
    audit_logs: true,
    data_encryption: true,
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleGeneralSettingsChange = (field: string, value: any) => {
    setGeneralSettings({ ...generalSettings, [field]: value });
  };

  const handleSystemSettingsChange = (field: string, value: any) => {
    setSystemSettings({ ...systemSettings, [field]: value });
  };

  const handleSecuritySettingsChange = (field: string, value: any) => {
    setSecuritySettings({ ...securitySettings, [field]: value });
  };

  const handleSaveSettings = async () => {
    try {
      // هنا يتم حفظ الإعدادات عبر API
      setSuccess('تم حفظ الإعدادات بنجاح');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('فشل في حفظ الإعدادات');
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleBackup = async () => {
    try {
      // هنا يتم إنشاء نسخة احتياطية
      setSuccess('تم إنشاء النسخة الاحتياطية بنجاح');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('فشل في إنشاء النسخة الاحتياطية');
      setTimeout(() => setError(''), 3000);
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        الإعدادات
      </Typography>

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="الإعدادات العامة" icon={<Business />} />
            <Tab label="إعدادات النظام" icon={<Notifications />} />
            <Tab label="الأمان" icon={<Security />} />
            <Tab label="النسخ الاحتياطي" icon={<Backup />} />
          </Tabs>
        </Box>

        {/* الإعدادات العامة */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم الصيدلية"
                value={generalSettings.pharmacy_name}
                onChange={(e) => handleGeneralSettingsChange('pharmacy_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="رقم الترخيص"
                value={generalSettings.pharmacy_license}
                onChange={(e) => handleGeneralSettingsChange('pharmacy_license', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="العنوان"
                multiline
                rows={2}
                value={generalSettings.address}
                onChange={(e) => handleGeneralSettingsChange('address', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={generalSettings.phone}
                onChange={(e) => handleGeneralSettingsChange('phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={generalSettings.email}
                onChange={(e) => handleGeneralSettingsChange('email', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="الرقم الضريبي"
                value={generalSettings.tax_number}
                onChange={(e) => handleGeneralSettingsChange('tax_number', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="العملة"
                value={generalSettings.currency}
                onChange={(e) => handleGeneralSettingsChange('currency', e.target.value)}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* إعدادات النظام */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                التنبيهات والإشعارات
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={systemSettings.low_stock_alert}
                    onChange={(e) => handleSystemSettingsChange('low_stock_alert', e.target.checked)}
                  />
                }
                label="تنبيه المخزون المنخفض"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="أيام تنبيه انتهاء الصلاحية"
                type="number"
                value={systemSettings.expiry_alert_days}
                onChange={(e) => handleSystemSettingsChange('expiry_alert_days', parseInt(e.target.value))}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                الأجهزة والمعدات
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={systemSettings.receipt_printer}
                    onChange={(e) => handleSystemSettingsChange('receipt_printer', e.target.checked)}
                  />
                }
                label="طابعة الإيصالات"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={systemSettings.barcode_scanner}
                    onChange={(e) => handleSystemSettingsChange('barcode_scanner', e.target.checked)}
                  />
                }
                label="قارئ الباركود"
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                الميزات الإضافية
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={systemSettings.loyalty_program}
                    onChange={(e) => handleSystemSettingsChange('loyalty_program', e.target.checked)}
                  />
                }
                label="برنامج الولاء"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={systemSettings.prescription_required_check}
                    onChange={(e) => handleSystemSettingsChange('prescription_required_check', e.target.checked)}
                  />
                }
                label="فحص الوصفة الطبية"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* إعدادات الأمان */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="انتهاء صلاحية كلمة المرور (أيام)"
                type="number"
                value={securitySettings.password_expiry_days}
                onChange={(e) => handleSecuritySettingsChange('password_expiry_days', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="الحد الأقصى لمحاولات تسجيل الدخول"
                type="number"
                value={securitySettings.max_login_attempts}
                onChange={(e) => handleSecuritySettingsChange('max_login_attempts', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="انتهاء الجلسة (دقائق)"
                type="number"
                value={securitySettings.session_timeout}
                onChange={(e) => handleSecuritySettingsChange('session_timeout', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.two_factor_auth}
                    onChange={(e) => handleSecuritySettingsChange('two_factor_auth', e.target.checked)}
                  />
                }
                label="المصادقة الثنائية"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.audit_logs}
                    onChange={(e) => handleSecuritySettingsChange('audit_logs', e.target.checked)}
                  />
                }
                label="سجلات المراجعة"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.data_encryption}
                    onChange={(e) => handleSecuritySettingsChange('data_encryption', e.target.checked)}
                  />
                }
                label="تشفير البيانات"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* النسخ الاحتياطي */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    النسخ الاحتياطي التلقائي
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={systemSettings.auto_backup}
                        onChange={(e) => handleSystemSettingsChange('auto_backup', e.target.checked)}
                      />
                    }
                    label="تفعيل النسخ الاحتياطي التلقائي"
                  />
                  <TextField
                    fullWidth
                    select
                    label="تكرار النسخ الاحتياطي"
                    value={systemSettings.backup_frequency}
                    onChange={(e) => handleSystemSettingsChange('backup_frequency', e.target.value)}
                    sx={{ mt: 2 }}
                    SelectProps={{
                      native: true,
                    }}
                  >
                    <option value="daily">يومي</option>
                    <option value="weekly">أسبوعي</option>
                    <option value="monthly">شهري</option>
                  </TextField>
                </CardContent>
                <CardActions>
                  <Button
                    variant="contained"
                    startIcon={<Backup />}
                    onClick={handleBackup}
                  >
                    إنشاء نسخة احتياطية الآن
                  </Button>
                </CardActions>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    النسخ الاحتياطية المحفوظة
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemText
                        primary="نسخة احتياطية - 2024/05/26"
                        secondary="حجم الملف: 15.2 MB"
                      />
                      <ListItemSecondaryAction>
                        <IconButton edge="end">
                          <Restore />
                        </IconButton>
                        <IconButton edge="end" color="error">
                          <Delete />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="نسخة احتياطية - 2024/05/25"
                        secondary="حجم الملف: 14.8 MB"
                      />
                      <ListItemSecondaryAction>
                        <IconButton edge="end">
                          <Restore />
                        </IconButton>
                        <IconButton edge="end" color="error">
                          <Delete />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* أزرار الحفظ */}
        <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSaveSettings}
            size="large"
          >
            حفظ الإعدادات
          </Button>
        </Box>
      </Paper>
    </Box>
  );
}
