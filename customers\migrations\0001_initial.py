# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=50, verbose_name='اسم العائلة')),
                ('phone_number', models.CharField(max_length=17, unique=True, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('gender', models.CharField(blank=True, choices=[('M', 'ذكر'), ('F', 'أنثى')], max_length=1, verbose_name='الجنس')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('allergies', models.TextField(blank=True, verbose_name='الحساسيات')),
                ('chronic_conditions', models.TextField(blank=True, verbose_name='الأمراض المزمنة')),
                ('emergency_contact', models.CharField(blank=True, max_length=100, verbose_name='جهة الاتصال في الطوارئ')),
                ('emergency_phone', models.CharField(blank=True, max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='هاتف الطوارئ')),
                ('insurance_company', models.CharField(blank=True, max_length=100, verbose_name='شركة التأمين')),
                ('insurance_number', models.CharField(blank=True, max_length=50, verbose_name='رقم التأمين')),
                ('insurance_expiry', models.DateField(blank=True, null=True, verbose_name='انتهاء التأمين')),
                ('loyalty_points', models.IntegerField(default=0, verbose_name='نقاط الولاء')),
                ('total_purchases', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='إجمالي المشتريات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['first_name', 'last_name'],
            },
        ),
        migrations.CreateModel(
            name='Doctor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الطبيب')),
                ('specialization', models.CharField(max_length=100, verbose_name='التخصص')),
                ('license_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الترخيص')),
                ('phone_number', models.CharField(blank=True, max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('clinic_name', models.CharField(blank=True, max_length=100, verbose_name='اسم العيادة')),
                ('clinic_address', models.TextField(blank=True, verbose_name='عنوان العيادة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'طبيب',
                'verbose_name_plural': 'الأطباء',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='LoyaltyProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم البرنامج')),
                ('points_per_riyal', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=5, verbose_name='نقاط لكل ريال')),
                ('riyal_per_point', models.DecimalField(decimal_places=2, default=Decimal('0.01'), max_digits=5, verbose_name='ريال لكل نقطة')),
                ('minimum_purchase', models.DecimalField(decimal_places=2, default=Decimal('10.00'), max_digits=10, verbose_name='الحد الأدنى للشراء')),
                ('minimum_redemption', models.IntegerField(default=100, verbose_name='الحد الأدنى للاستبدال')),
                ('expiry_months', models.IntegerField(default=12, verbose_name='انتهاء النقاط (شهور)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'برنامج الولاء',
                'verbose_name_plural': 'برامج الولاء',
            },
        ),
        migrations.CreateModel(
            name='Prescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prescription_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الوصفة')),
                ('prescription_date', models.DateField(verbose_name='تاريخ الوصفة')),
                ('diagnosis', models.TextField(blank=True, verbose_name='التشخيص')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('prescription_image', models.ImageField(blank=True, null=True, upload_to='prescriptions/', verbose_name='صورة الوصفة')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('partial', 'جزئي'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('dispensed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الصرف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'وصفة طبية',
                'verbose_name_plural': 'الوصفات الطبية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PrescriptionItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_prescribed', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية الموصوفة')),
                ('quantity_dispensed', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المصروفة')),
                ('dosage', models.CharField(blank=True, max_length=100, verbose_name='الجرعة')),
                ('frequency', models.CharField(blank=True, max_length=100, verbose_name='التكرار')),
                ('duration', models.CharField(blank=True, max_length=100, verbose_name='المدة')),
                ('instructions', models.TextField(blank=True, verbose_name='التعليمات')),
                ('unit_price', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='السعر الإجمالي')),
                ('is_dispensed', models.BooleanField(default=False, verbose_name='تم الصرف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'عنصر الوصفة',
                'verbose_name_plural': 'عناصر الوصفة',
            },
        ),
        migrations.CreateModel(
            name='LoyaltyTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('earned', 'مكتسب'), ('redeemed', 'مستبدل'), ('expired', 'منتهي'), ('adjusted', 'معدل')], max_length=20, verbose_name='نوع المعاملة')),
                ('points', models.IntegerField(verbose_name='النقاط')),
                ('description', models.CharField(max_length=200, verbose_name='الوصف')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ المعاملة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loyalty_transactions', to='customers.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'معاملة ولاء',
                'verbose_name_plural': 'معاملات الولاء',
                'ordering': ['-created_at'],
            },
        ),
    ]
