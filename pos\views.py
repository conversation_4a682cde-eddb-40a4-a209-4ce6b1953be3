from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Sum, Count
from django.utils import timezone
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST
from django.template.loader import render_to_string
from decimal import Decimal
import json

from .models import Sale, SaleItem, Return, ReturnItem, CashRegister, Receipt
from inventory.models import Medicine, Batch
from customers.models import Customer
from branches.models import Branch


@login_required
def sale_list_view(request):
    """قائمة المبيعات"""
    sales = Sale.objects.all().order_by('-created_at')

    # فلترة حسب التاريخ
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        sales = sales.filter(created_at__date__gte=date_from)
    if date_to:
        sales = sales.filter(created_at__date__lte=date_to)

    # فلترة حسب الحالة
    status = request.GET.get('status')
    if status:
        sales = sales.filter(status=status)

    # فلترة حسب الكاشير
    cashier = request.GET.get('cashier')
    if cashier:
        sales = sales.filter(cashier_id=cashier)

    # البحث
    search = request.GET.get('search')
    if search:
        sales = sales.filter(
            Q(sale_number__icontains=search) |
            Q(customer__first_name__icontains=search) |
            Q(customer__last_name__icontains=search)
        )

    # التصفح
    paginator = Paginator(sales, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات
    stats = {
        'total_sales': sales.count(),
        'total_amount': sales.aggregate(total=Sum('total_amount'))['total'] or 0,
        'completed_sales': sales.filter(status='completed').count(),
        'pending_sales': sales.filter(status='pending').count(),
    }

    context = {
        'page_obj': page_obj,
        'stats': stats,
        'status_choices': Sale.STATUS_CHOICES,
    }

    return render(request, 'pos/sale_list.html', context)


@login_required
def new_sale_view(request):
    """إنشاء بيع جديد"""
    # التحقق من وجود صندوق نقد مفتوح
    cash_register = CashRegister.objects.filter(
        cashier=request.user,
        is_open=True
    ).first()

    if not cash_register:
        messages.warning(request, 'يجب فتح صندوق النقد أولاً')
        return redirect('pos:cash_register')

    # الحصول على الأدوية
    medicines = Medicine.objects.filter(
        is_active=True,
        current_stock__gt=0
    ).select_related('category', 'manufacturer')

    # العملاء
    customers = Customer.objects.filter(is_active=True)

    context = {
        'medicines': medicines,
        'customers': customers,
        'cash_register': cash_register,
    }

    return render(request, 'pos/new_sale.html', context)


@login_required
@require_POST
def create_sale_view(request):
    """إنشاء بيع جديد"""
    try:
        data = json.loads(request.body)

        # التحقق من صندوق النقد
        cash_register = CashRegister.objects.filter(
            cashier=request.user,
            is_open=True
        ).first()

        if not cash_register:
            return JsonResponse({'error': 'صندوق النقد غير مفتوح'}, status=400)

        # إنشاء البيع
        sale = Sale.objects.create(
            cashier=request.user,
            customer_id=data.get('customer_id') if data.get('customer_id') else None,
            branch=request.user.branch,
            cash_register=cash_register,
            subtotal=Decimal(data['subtotal']),
            discount_amount=Decimal(data.get('discount_amount', 0)),
            tax_amount=Decimal(data.get('tax_amount', 0)),
            total_amount=Decimal(data['total_amount']),
            payment_method=data['payment_method'],
            amount_paid=Decimal(data['amount_paid']),
            change_amount=Decimal(data.get('change_amount', 0)),
            notes=data.get('notes', ''),
            status='completed'
        )

        # إضافة عناصر البيع
        for item_data in data['items']:
            medicine = Medicine.objects.get(id=item_data['medicine_id'])

            # التحقق من المخزون
            if medicine.current_stock < item_data['quantity']:
                sale.delete()
                return JsonResponse({
                    'error': f'المخزون غير كافي للدواء {medicine.name}'
                }, status=400)

            # إنشاء عنصر البيع
            SaleItem.objects.create(
                sale=sale,
                medicine=medicine,
                quantity=item_data['quantity'],
                unit_price=Decimal(item_data['unit_price']),
                total_price=Decimal(item_data['total_price']),
                discount_amount=Decimal(item_data.get('discount_amount', 0))
            )

            # تحديث المخزون
            medicine.current_stock -= item_data['quantity']
            medicine.save()

        # إنشاء الإيصال
        receipt = Receipt.objects.create(
            sale=sale,
            receipt_number=sale.sale_number,
            printed_by=request.user
        )

        return JsonResponse({
            'success': True,
            'sale_id': sale.id,
            'receipt_id': receipt.id,
            'message': 'تم إنشاء البيع بنجاح'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def sale_detail_view(request, sale_id):
    """تفاصيل البيع"""
    sale = get_object_or_404(Sale, id=sale_id)
    sale_items = SaleItem.objects.filter(sale=sale).select_related('medicine')

    context = {
        'sale': sale,
        'sale_items': sale_items,
    }

    return render(request, 'pos/sale_detail.html', context)


@login_required
def receipt_view(request, sale_id):
    """عرض الإيصال"""
    sale = get_object_or_404(Sale, id=sale_id)
    sale_items = SaleItem.objects.filter(sale=sale).select_related('medicine')

    context = {
        'sale': sale,
        'sale_items': sale_items,
    }

    return render(request, 'pos/receipt.html', context)


@login_required
def print_receipt_view(request, sale_id):
    """طباعة الإيصال"""
    sale = get_object_or_404(Sale, id=sale_id)
    sale_items = SaleItem.objects.filter(sale=sale).select_related('medicine')

    # تحديث حالة الطباعة
    receipt = Receipt.objects.filter(sale=sale).first()
    if receipt:
        receipt.is_printed = True
        receipt.printed_at = timezone.now()
        receipt.save()

    context = {
        'sale': sale,
        'sale_items': sale_items,
    }

    return render(request, 'pos/print_receipt.html', context)


@login_required
def cash_register_view(request):
    """إدارة صندوق النقد"""
    cash_register = CashRegister.objects.filter(
        cashier=request.user
    ).order_by('-opened_at').first()

    # إحصائيات اليوم
    today_sales = Sale.objects.filter(
        cashier=request.user,
        created_at__date=timezone.now().date(),
        status='completed'
    )

    stats = {
        'total_sales': today_sales.count(),
        'total_amount': today_sales.aggregate(total=Sum('total_amount'))['total'] or 0,
        'cash_sales': today_sales.filter(payment_method='cash').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
        'card_sales': today_sales.filter(payment_method='card').aggregate(
            total=Sum('total_amount')
        )['total'] or 0,
    }

    context = {
        'cash_register': cash_register,
        'stats': stats,
    }

    return render(request, 'pos/cash_register.html', context)


@login_required
@require_POST
def open_cash_register_view(request):
    """فتح صندوق النقد"""
    # التحقق من عدم وجود صندوق مفتوح
    existing = CashRegister.objects.filter(
        cashier=request.user,
        is_open=True
    ).exists()

    if existing:
        messages.error(request, 'يوجد صندوق نقد مفتوح بالفعل')
        return redirect('pos:cash_register')

    opening_amount = Decimal(request.POST.get('opening_amount', 0))

    cash_register = CashRegister.objects.create(
        cashier=request.user,
        branch=request.user.branch,
        opening_amount=opening_amount,
        current_amount=opening_amount,
        is_open=True
    )

    messages.success(request, 'تم فتح صندوق النقد بنجاح')
    return redirect('pos:cash_register')


@login_required
@require_POST
def close_cash_register_view(request):
    """إغلاق صندوق النقد"""
    cash_register = get_object_or_404(
        CashRegister,
        cashier=request.user,
        is_open=True
    )

    closing_amount = Decimal(request.POST.get('closing_amount', 0))
    notes = request.POST.get('notes', '')

    cash_register.closing_amount = closing_amount
    cash_register.closed_at = timezone.now()
    cash_register.is_open = False
    cash_register.notes = notes

    # حساب الفرق
    expected_amount = cash_register.opening_amount + cash_register.total_sales_amount
    cash_register.difference_amount = closing_amount - expected_amount

    cash_register.save()

    messages.success(request, 'تم إغلاق صندوق النقد بنجاح')
    return redirect('pos:cash_register')


@login_required
def search_medicine_api(request):
    """البحث عن الأدوية - API"""
    query = request.GET.get('q', '')

    if len(query) < 2:
        return JsonResponse({'medicines': []})

    medicines = Medicine.objects.filter(
        Q(name__icontains=query) |
        Q(generic_name__icontains=query) |
        Q(barcode__icontains=query),
        is_active=True,
        current_stock__gt=0
    ).select_related('category')[:10]

    data = {
        'medicines': [
            {
                'id': m.id,
                'name': m.name,
                'generic_name': m.generic_name,
                'barcode': m.barcode,
                'selling_price': str(m.selling_price),
                'current_stock': m.current_stock,
                'category': m.category.name if m.category else '',
                'unit': m.unit,
            } for m in medicines
        ]
    }

    return JsonResponse(data)


@login_required
def get_medicine_api(request, medicine_id):
    """الحصول على تفاصيل دواء - API"""
    try:
        medicine = Medicine.objects.get(id=medicine_id, is_active=True)

        data = {
            'id': medicine.id,
            'name': medicine.name,
            'generic_name': medicine.generic_name,
            'barcode': medicine.barcode,
            'selling_price': str(medicine.selling_price),
            'current_stock': medicine.current_stock,
            'category': medicine.category.name if medicine.category else '',
            'unit': medicine.unit,
            'description': medicine.description,
        }

        return JsonResponse(data)

    except Medicine.DoesNotExist:
        return JsonResponse({'error': 'الدواء غير موجود'}, status=404)
