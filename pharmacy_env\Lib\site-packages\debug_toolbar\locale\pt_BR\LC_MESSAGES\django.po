# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014
# <PERSON>, 2009
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-20 17:23+0100\n"
"PO-Revision-Date: 2014-04-25 19:53+0000\n"
"Last-Translator: Aymeric Augustin <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (http://www.transifex.com/projects/p/"
"django-debug-toolbar/language/pt_BR/)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr ""

#: panels/cache.py:180
msgid "Cache"
msgstr "Cache"

#: panels/cache.py:186
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d chamada em %(time).2fms"
msgstr[1] "%(cache_calls)d chamadas em %(time).2fms"

#: panels/cache.py:195
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Chamadas ao cache de %(count)d backend"
msgstr[1] "Chamadas ao cache de %(count)d backends"

#: panels/headers.py:31
msgid "Headers"
msgstr "Cabeçalhos"

#: panels/history/panel.py:18 panels/history/panel.py:19
msgid "History"
msgstr ""

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Profiling"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "Interceptar redirecionamentos"

#: panels/request.py:16
msgid "Request"
msgstr "Requisição"

#: panels/request.py:36
msgid "<no view>"
msgstr "<nenhuma vista>"

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<indisponível>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Configurações"

#: panels/settings.py:20
#, fuzzy, python-format
#| msgid "Settings from <code>%s</code>"
msgid "Settings from %s"
msgstr "Configurações em: <code>%s</code>"

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d receptor de 1 sinal"
msgstr[1] "%(num_receivers)d receptores de 1 sinal"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d receptor de %(num_signals)d sinais"
msgstr[1] "%(num_receivers)d receptores de %(num_signals)d sinais"

#: panels/signals.py:67
msgid "Signals"
msgstr "Sinais"

#: panels/sql/panel.py:23
msgid "Autocommit"
msgstr "Autocommit"

#: panels/sql/panel.py:24
msgid "Read uncommitted"
msgstr "Read uncommitted"

#: panels/sql/panel.py:25
msgid "Read committed"
msgstr "Read committed"

#: panels/sql/panel.py:26
msgid "Repeatable read"
msgstr "Leitura repetida"

#: panels/sql/panel.py:27
msgid "Serializable"
msgstr "Variável"

#: panels/sql/panel.py:39
msgid "Idle"
msgstr "Ocioso"

#: panels/sql/panel.py:40
msgid "Active"
msgstr "Ação"

#: panels/sql/panel.py:41
msgid "In transaction"
msgstr "Na transação"

#: panels/sql/panel.py:42
msgid "In error"
msgstr "Erro"

#: panels/sql/panel.py:43
msgid "Unknown"
msgstr "Desconhecido"

#: panels/sql/panel.py:130
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:135
#, fuzzy, python-format
#| msgid "%(cache_calls)d call in %(time).2fms"
#| msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] "%(cache_calls)d chamada em %(time).2fms"
msgstr[1] "%(cache_calls)d chamadas em %(time).2fms"

#: panels/sql/panel.py:147
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] ""
msgstr[1] ""

#: panels/staticfiles.py:84
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr ""
"Arquivos estáticos (%(num_found)s encontrados, %(num_used)s sendo utilizados)"

#: panels/staticfiles.py:105
msgid "Static files"
msgstr "Arquivos estáticos"

#: panels/staticfiles.py:111
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s arquivo utilizado"
msgstr[1] "%(num_used)s arquivos utilizados"

#: panels/templates/panel.py:143
msgid "Templates"
msgstr "Templates"

#: panels/templates/panel.py:148
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Templates (%(num_templates)s renderizados)"

#: panels/templates/panel.py:180
msgid "No origin"
msgstr ""

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr "Total: %0.2fms"

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Tempo"

#: panels/timer.py:44
msgid "User CPU time"
msgstr "Tempo de CPU do usuário"

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f ms"

#: panels/timer.py:45
msgid "System CPU time"
msgstr "Tempo de CPU do sistema"

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f ms"

#: panels/timer.py:46
msgid "Total CPU time"
msgstr "Tempo total de CPU"

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f ms"

#: panels/timer.py:47
msgid "Elapsed time"
msgstr "Tempo decorrido"

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f ms"

#: panels/timer.py:49
msgid "Context switches"
msgstr "Mudanças de contexto"

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d voluntário, %(ivcsw)d involuntário"

#: panels/versions.py:19
msgid "Versions"
msgstr "Versões"

#: templates/debug_toolbar/base.html:22
msgid "Hide toolbar"
msgstr "Ocultar barra de ferramentas"

#: templates/debug_toolbar/base.html:22
msgid "Hide"
msgstr "Esconder"

#: templates/debug_toolbar/base.html:29
msgid "Show toolbar"
msgstr "Mostrar barra de ferramentas"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Desativar para próximas requisições"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Habilitar para próximas requisições"

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Resumo"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Total de chamadas"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Tempo total"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Acessos ao cache"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Falhas de cache"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Comandos"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Chamadas"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Tempo (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Tipo"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Argumentos"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Argumentos"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Backend"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "Cabeçalhos de Requisição"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Chave"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Valor"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "Cabeçalhos de Resposta"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "Ambiente WSGI"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""
"Uma vez que o ambiente WSGI herda o ambiente do servidor, apenas um "
"subconjunto significativo é mostrado abaixo."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr ""

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Caminho"

#: templates/debug_toolbar/panels/history.html:12
#, fuzzy
#| msgid "Request headers"
msgid "Request Variables"
msgstr "Cabeçalhos de Requisição"

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr ""

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Ação"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Variável"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Chamar"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "CumTime"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "Per"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "TotTime"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Contagem"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Ver informação"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "Função View"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "Nome da URL"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Cookies"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Sem Cookies"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Dados de Sessão"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Sem dados de Sessão"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "Dados de GET"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Não há dados de GET"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "Dados de POST"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Não há dados de POST"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Configuração"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Sinais"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Recebedores"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s consulta"
msgstr[1] "%(num)s consultas"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Query"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Linha do tempo"

#: templates/debug_toolbar/panels/sql.html:52
#, fuzzy, python-format
#| msgid "%(count)s message"
#| msgid_plural "%(count)s messages"
msgid "%(count)s similar queries."
msgstr "%(count)s mensagem"

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr ""

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Conexão:"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Nível de isolamento:"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "Status da transação:"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(unknown)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr "Nenhuma consulta SQL foi registrada durante esta requisição."

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "SQL explicada"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "SQL Executada"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Banco de dados"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "SQL perfilado"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Erro"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "SQL selecionada"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Conjunto vazio"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Caminho do arquivo estático"
msgstr[1] "Caminho dos arquivos estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(prefixo %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Nenhum"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Arquivo estático de app"
msgstr[1] "Arquivos estáticos de apps"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] "Arquivo estático"
msgstr[1] "Arquivos estáticos"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s arquivo"
msgstr[1] "%(payload_count)s arquivos"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Localização"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Origem do Template:"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] "Caminho do Template"
msgstr[1] "Caminho do Templates"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] "Template"
msgstr[1] "Templates"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Alternar contexto"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] ""
msgstr[1] "Processador do Contexto"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Uso de recursos"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Recurso"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Cronometragem do Navegador"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Atributo de Cronometragem"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Milissegundos desde início de navegação (+length)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr ""

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Nome"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Versão"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Localização:"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""
"O Django Debug Toolbar interceptou um redirecionamento para a URL acima para "
"fins de visualização de depuração. Você pode clicar no link acima para "
"continuar com o redirecionamento normalmente."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
"Os dados para este painel não está mais disponível. Por favor, recarregue a "
"página e tente novamente."
