from django.contrib import admin
from .models import Branch, BranchSettings


class BranchSettingsInline(admin.StackedInline):
    model = BranchSettings
    extra = 0


@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'city', 'manager', 'is_active', 'is_main_branch')
    list_filter = ('is_active', 'is_main_branch', 'city')
    search_fields = ('name', 'code', 'address', 'license_number')
    ordering = ('name',)
    inlines = [BranchSettingsInline]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'code', 'address', 'city')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone_number', 'email')
        }),
        ('الإدارة', {
            'fields': ('manager',)
        }),
        ('الترخيص', {
            'fields': ('license_number', 'license_expiry_date')
        }),
        ('الإعدادات', {
            'fields': ('is_active', 'is_main_branch', 'opening_time', 'closing_time')
        }),
    )


@admin.register(BranchSettings)
class BranchSettingsAdmin(admin.ModelAdmin):
    list_display = ('branch', 'currency_code', 'tax_rate', 'low_stock_threshold')
    list_filter = ('currency_code', 'auto_print_receipt', 'allow_negative_stock')
    search_fields = ('branch__name',)

    fieldsets = (
        ('الفرع', {
            'fields': ('branch',)
        }),
        ('الإعدادات المالية', {
            'fields': ('tax_rate', 'currency_code', 'currency_symbol')
        }),
        ('إعدادات الفاتورة', {
            'fields': ('receipt_header', 'receipt_footer', 'auto_print_receipt')
        }),
        ('إعدادات المخزون', {
            'fields': ('low_stock_threshold', 'expiry_alert_days', 'allow_negative_stock')
        }),
        ('النسخ الاحتياطي', {
            'fields': ('auto_backup_enabled', 'backup_frequency_hours')
        }),
    )
