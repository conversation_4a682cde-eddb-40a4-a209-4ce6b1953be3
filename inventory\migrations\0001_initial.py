# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز الفئة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Manufacturer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الشركة')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز الشركة')),
                ('country', models.CharField(max_length=50, verbose_name='البلد')),
                ('contact_info', models.TextField(blank=True, verbose_name='معلومات الاتصال')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'شركة تصنيع',
                'verbose_name_plural': 'شركات التصنيع',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Medicine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الدواء')),
                ('generic_name', models.CharField(blank=True, max_length=200, verbose_name='الاسم العلمي')),
                ('barcode', models.CharField(max_length=50, unique=True, verbose_name='الباركود')),
                ('unit', models.CharField(choices=[('tablet', 'قرص'), ('capsule', 'كبسولة'), ('bottle', 'زجاجة'), ('box', 'علبة'), ('tube', 'أنبوب'), ('vial', 'قارورة'), ('sachet', 'كيس'), ('piece', 'قطعة')], default='tablet', max_length=20, verbose_name='الوحدة')),
                ('strength', models.CharField(blank=True, max_length=50, verbose_name='التركيز')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر التكلفة')),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر البيع')),
                ('minimum_stock', models.IntegerField(default=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock', models.IntegerField(default=1000, validators=[django.core.validators.MinValueValidator(1)], verbose_name='الحد الأقصى للمخزون')),
                ('requires_prescription', models.BooleanField(default=False, verbose_name='يتطلب وصفة طبية')),
                ('is_controlled', models.BooleanField(default=False, verbose_name='دواء مراقب')),
                ('storage_conditions', models.TextField(blank=True, verbose_name='شروط التخزين')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'دواء',
                'verbose_name_plural': 'الأدوية',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='StockAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('expiry_warning', 'تحذير انتهاء صلاحية'), ('expired', 'منتهي الصلاحية'), ('out_of_stock', 'نفاد المخزون')], max_length=20, verbose_name='نوع التنبيه')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('current_stock', models.IntegerField(verbose_name='المخزون الحالي')),
                ('threshold', models.IntegerField(blank=True, null=True, verbose_name='الحد المحدد')),
                ('is_acknowledged', models.BooleanField(default=False, verbose_name='تم الاطلاع')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاطلاع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'تنبيه مخزون',
                'verbose_name_plural': 'تنبيهات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('purchase', 'شراء'), ('sale', 'بيع'), ('return', 'إرجاع'), ('transfer_in', 'تحويل وارد'), ('transfer_out', 'تحويل صادر'), ('adjustment', 'تسوية'), ('expired', 'منتهي الصلاحية'), ('damaged', 'تالف')], max_length=20, verbose_name='نوع الحركة')),
                ('quantity', models.IntegerField(verbose_name='الكمية')),
                ('quantity_change', models.IntegerField(verbose_name='تغيير الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='سعر الوحدة')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('reference_number', models.CharField(blank=True, max_length=50, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الحركة')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Batch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=50, verbose_name='رقم الدفعة')),
                ('manufacturing_date', models.DateField(verbose_name='تاريخ التصنيع')),
                ('expiry_date', models.DateField(verbose_name='تاريخ انتهاء الصلاحية')),
                ('initial_quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية الأولية')),
                ('current_quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية الحالية')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر التكلفة')),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر البيع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='batches', to='branches.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['expiry_date'],
            },
        ),
    ]
