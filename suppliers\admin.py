from django.contrib import admin
from .models import Supplier, PurchaseOrder, PurchaseOrderItem, Payment, SupplierOffer, SupplierOfferItem


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'contact_person', 'phone_number', 'city', 'country', 'is_active')
    list_filter = ('country', 'city', 'is_active', 'created_at')
    search_fields = ('name', 'code', 'contact_person', 'phone_number', 'email')
    ordering = ('name',)

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'code', 'contact_person')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone_number', 'email', 'address', 'city', 'country')
        }),
        ('المعلومات المالية', {
            'fields': ('credit_limit', 'payment_terms', 'tax_number')
        }),
        ('الحالة والملاحظات', {
            'fields': ('is_active', 'notes')
        }),
    )


class PurchaseOrderItemInline(admin.TabularInline):
    model = PurchaseOrderItem
    extra = 0
    readonly_fields = ('total_cost', 'is_fully_received', 'remaining_quantity')


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ('order_number', 'supplier', 'branch', 'order_date', 'total_amount', 'status', 'payment_status')
    list_filter = ('status', 'payment_status', 'branch', 'order_date', 'created_at')
    search_fields = ('order_number', 'supplier__name', 'notes')
    ordering = ('-created_at',)
    readonly_fields = ('order_number', 'subtotal', 'tax_amount', 'total_amount', 'remaining_balance')
    inlines = [PurchaseOrderItemInline]

    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('order_number', 'supplier', 'branch', 'created_by', 'order_date')
        }),
        ('التواريخ', {
            'fields': ('expected_delivery_date', 'actual_delivery_date')
        }),
        ('المبالغ المالية', {
            'fields': ('subtotal', 'discount_amount', 'tax_amount', 'shipping_cost', 'total_amount')
        }),
        ('الدفع', {
            'fields': ('paid_amount', 'remaining_balance', 'payment_status')
        }),
        ('الحالة والملاحظات', {
            'fields': ('status', 'notes')
        }),
    )


@admin.register(PurchaseOrderItem)
class PurchaseOrderItemAdmin(admin.ModelAdmin):
    list_display = ('purchase_order', 'medicine', 'quantity_ordered', 'quantity_received', 'unit_cost', 'total_cost')
    list_filter = ('purchase_order__status', 'purchase_order__supplier')
    search_fields = ('purchase_order__order_number', 'medicine__name', 'batch_number')
    ordering = ('-purchase_order__created_at',)
    readonly_fields = ('total_cost', 'is_fully_received', 'remaining_quantity')

    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('purchase_order', 'medicine')
        }),
        ('الكميات', {
            'fields': ('quantity_ordered', 'quantity_received', 'is_fully_received', 'remaining_quantity')
        }),
        ('الأسعار', {
            'fields': ('unit_cost', 'total_cost')
        }),
        ('معلومات الدفعة', {
            'fields': ('batch_number', 'manufacturing_date', 'expiry_date')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('payment_number', 'supplier', 'purchase_order', 'amount', 'payment_method', 'payment_date', 'processed_by')
    list_filter = ('payment_method', 'payment_date', 'supplier')
    search_fields = ('payment_number', 'supplier__name', 'purchase_order__order_number', 'reference_number')
    ordering = ('-payment_date',)
    readonly_fields = ('payment_number',)

    fieldsets = (
        ('معلومات الدفعة', {
            'fields': ('payment_number', 'supplier', 'purchase_order', 'processed_by')
        }),
        ('تفاصيل الدفع', {
            'fields': ('amount', 'payment_method', 'payment_date', 'reference_number')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )


class SupplierOfferItemInline(admin.TabularInline):
    model = SupplierOfferItem
    extra = 0


@admin.register(SupplierOffer)
class SupplierOfferAdmin(admin.ModelAdmin):
    list_display = ('title', 'supplier', 'start_date', 'end_date', 'discount_percentage', 'minimum_quantity', 'is_active', 'is_valid')
    list_filter = ('is_active', 'supplier', 'start_date', 'end_date')
    search_fields = ('title', 'supplier__name', 'description')
    ordering = ('-start_date',)
    inlines = [SupplierOfferItemInline]

    fieldsets = (
        ('معلومات العرض', {
            'fields': ('supplier', 'title', 'description')
        }),
        ('فترة العرض', {
            'fields': ('start_date', 'end_date', 'is_active')
        }),
        ('شروط العرض', {
            'fields': ('discount_percentage', 'minimum_quantity')
        }),
    )


@admin.register(SupplierOfferItem)
class SupplierOfferItemAdmin(admin.ModelAdmin):
    list_display = ('offer', 'medicine', 'special_price')
    list_filter = ('offer__supplier', 'offer__is_active')
    search_fields = ('offer__title', 'medicine__name')
    ordering = ('offer__start_date',)
