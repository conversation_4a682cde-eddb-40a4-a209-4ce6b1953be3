{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"PaperComponent\", \"ownerState\", \"children\", \"paperSlotProps\", \"paperClasses\", \"onPaperClick\", \"onPaperTouchStart\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport Grow from '@mui/material/Grow';\nimport Fade from '@mui/material/Fade';\nimport MuiPaper from '@mui/material/Paper';\nimport MuiPopper from '@mui/material/Popper';\nimport BaseFocusTrap from '@mui/material/Unstable_TrapFocus';\nimport { unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback, unstable_ownerDocument as ownerDocument, unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { getPickerPopperUtilityClass } from \"./pickerPopperClasses.js\";\nimport { executeInTheNextEventLoopTick, getActiveElement } from \"../../utils/utils.js\";\nimport { usePickerPrivateContext } from \"../../hooks/usePickerPrivateContext.js\";\nimport { usePickerContext } from \"../../../hooks/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPickerPopperUtilityClass, classes);\n};\nconst PickerPopperRoot = styled(MuiPopper, {\n  name: 'MuiPickerPopper',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  zIndex: theme.zIndex.modal\n}));\nconst PickerPopperPaper = styled(MuiPaper, {\n  name: 'MuiPickerPopper',\n  slot: 'Paper'\n})({\n  outline: 0,\n  transformOrigin: 'top center',\n  variants: [{\n    props: ({\n      popperPlacement\n    }) => ['top', 'top-start', 'top-end'].includes(popperPlacement),\n    style: {\n      transformOrigin: 'bottom center'\n    }\n  }]\n});\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Based on @mui/material/ClickAwayListener without the customization.\n * We can probably strip away even more since children won't be portaled.\n * @param {boolean} active Only listen to clicks when the popper is opened.\n * @param {(event: MouseEvent | TouchEvent) => void} onClickAway The callback to call when clicking outside the popper.\n * @returns {Array} The ref and event handler to listen to the outside clicks.\n */\nfunction useClickAwayListener(active, onClickAway) {\n  const movedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  React.useEffect(() => {\n    if (!active) {\n      return undefined;\n    }\n\n    // Ensure that this hook is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    function armClickAwayListener() {\n      activatedRef.current = true;\n    }\n    document.addEventListener('mousedown', armClickAwayListener, true);\n    document.addEventListener('touchstart', armClickAwayListener, true);\n    return () => {\n      document.removeEventListener('mousedown', armClickAwayListener, true);\n      document.removeEventListener('touchstart', armClickAwayListener, true);\n      activatedRef.current = false;\n    };\n  }, [active]);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    if (!activatedRef.current) {\n      return;\n    }\n\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!nodeRef.current ||\n    // is a TouchEvent?\n    'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(event.target) || nodeRef.current.contains(event.target);\n    }\n    if (!insideDOM && !insideReactTree) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const handleSynthetic = () => {\n    syntheticEventRef.current = true;\n  };\n  React.useEffect(() => {\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener('touchstart', handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener('touchstart', handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  React.useEffect(() => {\n    // TODO This behavior is not tested automatically\n    // It's unclear whether this is due to different update semantics in test (batched in act() vs discrete on click).\n    // Or if this is a timing related issues due to different Transition components\n    // Once we get rid of all the manual scheduling (for example setTimeout(update, 0)) we can revisit this code+test.\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener('click', handleClickAway);\n      return () => {\n        doc.removeEventListener('click', handleClickAway);\n        // cleanup `handleClickAway`\n        syntheticEventRef.current = false;\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  return [nodeRef, handleSynthetic, handleSynthetic];\n}\nconst PickerPopperPaperWrapper = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      PaperComponent,\n      ownerState,\n      children,\n      paperSlotProps,\n      paperClasses,\n      onPaperClick,\n      onPaperTouchStart\n      // picks up the style props provided by `Transition`\n      // https://mui.com/material-ui/transitions/#child-requirement\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const paperProps = useSlotProps({\n    elementType: PaperComponent,\n    externalSlotProps: paperSlotProps,\n    additionalProps: {\n      tabIndex: -1,\n      elevation: 8,\n      ref\n    },\n    className: paperClasses,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(PaperComponent, _extends({}, other, paperProps, {\n    onClick: event => {\n      onPaperClick(event);\n      paperProps.onClick?.(event);\n    },\n    onTouchStart: event => {\n      onPaperTouchStart(event);\n      paperProps.onTouchStart?.(event);\n    },\n    ownerState: ownerState,\n    children: children\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") PickerPopperPaperWrapper.displayName = \"PickerPopperPaperWrapper\";\nexport function PickerPopper(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickerPopper'\n  });\n  const {\n    children,\n    placement = 'bottom-start',\n    slots,\n    slotProps,\n    classes: classesProp\n  } = props;\n  const {\n    open,\n    popupRef,\n    reduceAnimations\n  } = usePickerContext();\n  const {\n    dismissViews,\n    getCurrentViewMode,\n    onPopperExited,\n    triggerElement,\n    viewContainerRole\n  } = usePickerPrivateContext();\n  React.useEffect(() => {\n    function handleKeyDown(nativeEvent) {\n      if (open && nativeEvent.key === 'Escape') {\n        dismissViews();\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [dismissViews, open]);\n  const lastFocusedElementRef = React.useRef(null);\n  React.useEffect(() => {\n    if (viewContainerRole === 'tooltip' || getCurrentViewMode() === 'field') {\n      return;\n    }\n    if (open) {\n      lastFocusedElementRef.current = getActiveElement(document);\n    } else if (lastFocusedElementRef.current && lastFocusedElementRef.current instanceof HTMLElement) {\n      // make sure the button is flushed with updated label, before returning focus to it\n      // avoids issue, where screen reader could fail to announce selected date after selection\n      setTimeout(() => {\n        if (lastFocusedElementRef.current instanceof HTMLElement) {\n          lastFocusedElementRef.current.focus();\n        }\n      });\n    }\n  }, [open, viewContainerRole, getCurrentViewMode]);\n  const classes = useUtilityClasses(classesProp);\n  const {\n    ownerState: pickerOwnerState,\n    rootRefObject\n  } = usePickerPrivateContext();\n  const ownerState = _extends({}, pickerOwnerState, {\n    popperPlacement: placement\n  });\n  const handleClickAway = useEventCallback(() => {\n    if (viewContainerRole === 'tooltip') {\n      executeInTheNextEventLoopTick(() => {\n        if (rootRefObject.current?.contains(getActiveElement(document)) || popupRef.current?.contains(getActiveElement(document))) {\n          return;\n        }\n        dismissViews();\n      });\n    } else {\n      dismissViews();\n    }\n  });\n  const [clickAwayRef, onPaperClick, onPaperTouchStart] = useClickAwayListener(open, handleClickAway);\n  const paperRef = React.useRef(null);\n  const handleRef = useForkRef(paperRef, popupRef);\n  const handlePaperRef = useForkRef(handleRef, clickAwayRef);\n  const handleKeyDown = event => {\n    if (event.key === 'Escape') {\n      // stop the propagation to avoid closing parent modal\n      event.stopPropagation();\n      dismissViews();\n    }\n  };\n  const Transition = slots?.desktopTransition ?? reduceAnimations ? Fade : Grow;\n  const FocusTrap = slots?.desktopTrapFocus ?? BaseFocusTrap;\n  const Paper = slots?.desktopPaper ?? PickerPopperPaper;\n  const Popper = slots?.popper ?? PickerPopperRoot;\n  const popperProps = useSlotProps({\n    elementType: Popper,\n    externalSlotProps: slotProps?.popper,\n    additionalProps: {\n      transition: true,\n      role: viewContainerRole == null ? undefined : viewContainerRole,\n      open,\n      placement,\n      anchorEl: triggerElement,\n      onKeyDown: handleKeyDown\n    },\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Popper, _extends({}, popperProps, {\n    children: ({\n      TransitionProps\n    }) => /*#__PURE__*/_jsx(FocusTrap, _extends({\n      open: open,\n      disableAutoFocus: true\n      // pickers are managing focus position manually\n      // without this prop the focus is returned to the button before `aria-label` is updated\n      // which would force screen readers to read too old label\n      ,\n\n      disableRestoreFocus: true,\n      disableEnforceFocus: viewContainerRole === 'tooltip',\n      isEnabled: () => true\n    }, slotProps?.desktopTrapFocus, {\n      children: /*#__PURE__*/_jsx(Transition, _extends({}, TransitionProps, slotProps?.desktopTransition, {\n        onExited: event => {\n          onPopperExited?.();\n          slotProps?.desktopTransition?.onExited?.(event);\n          TransitionProps?.onExited?.();\n        },\n        children: /*#__PURE__*/_jsx(PickerPopperPaperWrapper, {\n          PaperComponent: Paper,\n          ownerState: ownerState,\n          ref: handlePaperRef,\n          onPaperClick: onPaperClick,\n          onPaperTouchStart: onPaperTouchStart,\n          paperClasses: classes.paper,\n          paperSlotProps: slotProps?.desktopPaper,\n          children: children\n        })\n      }))\n    }))\n  }));\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useSlotProps", "Grow", "Fade", "MuiPaper", "MuiPopper", "BaseFocusTrap", "unstable_useForkRef", "useForkRef", "unstable_useEventCallback", "useEventCallback", "unstable_ownerDocument", "ownerDocument", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "getPickerPopperUtilityClass", "executeInTheNextEventLoopTick", "getActiveElement", "usePickerPrivateContext", "usePickerContext", "jsx", "_jsx", "useUtilityClasses", "classes", "slots", "root", "paper", "PickerPopperRoot", "name", "slot", "theme", "zIndex", "modal", "PickerPopperPaper", "outline", "transform<PERSON><PERSON>in", "variants", "props", "popperPlacement", "includes", "style", "clickedRootScrollbar", "event", "doc", "documentElement", "clientWidth", "clientX", "clientHeight", "clientY", "useClickAwayListener", "active", "onClickAway", "movedRef", "useRef", "syntheticEventRef", "nodeRef", "activatedRef", "useEffect", "undefined", "armClickAwayListener", "current", "document", "addEventListener", "removeEventListener", "handleClickAway", "insideReactTree", "insideDOM", "<PERSON><PERSON><PERSON>", "indexOf", "contains", "target", "handleSynthetic", "handleTouchMove", "PickerPopperPaperWrapper", "forwardRef", "ref", "PaperComponent", "ownerState", "children", "paperSlotProps", "paperClasses", "onPaperClick", "onPaperTouchStart", "other", "paperProps", "elementType", "externalSlotProps", "additionalProps", "tabIndex", "elevation", "className", "onClick", "onTouchStart", "process", "env", "NODE_ENV", "displayName", "Picker<PERSON><PERSON><PERSON>", "inProps", "placement", "slotProps", "classesProp", "open", "popupRef", "reduceAnimations", "dismissViews", "getCurrentViewMode", "onPopperExited", "triggerElement", "viewContainerRole", "handleKeyDown", "nativeEvent", "key", "lastFocusedElementRef", "HTMLElement", "setTimeout", "focus", "pickerOwnerState", "rootRefObject", "clickAwayRef", "paperRef", "handleRef", "handlePaperRef", "stopPropagation", "Transition", "desktopTransition", "FocusTrap", "desktopTrapFocus", "Paper", "desktopPaper", "<PERSON><PERSON>", "popper", "popperProps", "transition", "role", "anchorEl", "onKeyDown", "TransitionProps", "disableAutoFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableEnforceFocus", "isEnabled", "onExited"], "sources": ["D:/pos/frontend/node_modules/@mui/x-date-pickers/esm/internals/components/PickerPopper/PickerPopper.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"PaperComponent\", \"ownerState\", \"children\", \"paperSlotProps\", \"paperClasses\", \"onPaperClick\", \"onPaperTouchStart\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport Grow from '@mui/material/Grow';\nimport Fade from '@mui/material/Fade';\nimport MuiPaper from '@mui/material/Paper';\nimport MuiPopper from '@mui/material/Popper';\nimport BaseFocusTrap from '@mui/material/Unstable_TrapFocus';\nimport { unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback, unstable_ownerDocument as ownerDocument, unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { getPickerPopperUtilityClass } from \"./pickerPopperClasses.js\";\nimport { executeInTheNextEventLoopTick, getActiveElement } from \"../../utils/utils.js\";\nimport { usePickerPrivateContext } from \"../../hooks/usePickerPrivateContext.js\";\nimport { usePickerContext } from \"../../../hooks/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPickerPopperUtilityClass, classes);\n};\nconst PickerPopperRoot = styled(MuiPopper, {\n  name: 'MuiPickerPopper',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  zIndex: theme.zIndex.modal\n}));\nconst PickerPopperPaper = styled(MuiPaper, {\n  name: 'MuiPickerPopper',\n  slot: 'Paper'\n})({\n  outline: 0,\n  transformOrigin: 'top center',\n  variants: [{\n    props: ({\n      popperPlacement\n    }) => ['top', 'top-start', 'top-end'].includes(popperPlacement),\n    style: {\n      transformOrigin: 'bottom center'\n    }\n  }]\n});\nfunction clickedRootScrollbar(event, doc) {\n  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;\n}\n/**\n * Based on @mui/material/ClickAwayListener without the customization.\n * We can probably strip away even more since children won't be portaled.\n * @param {boolean} active Only listen to clicks when the popper is opened.\n * @param {(event: MouseEvent | TouchEvent) => void} onClickAway The callback to call when clicking outside the popper.\n * @returns {Array} The ref and event handler to listen to the outside clicks.\n */\nfunction useClickAwayListener(active, onClickAway) {\n  const movedRef = React.useRef(false);\n  const syntheticEventRef = React.useRef(false);\n  const nodeRef = React.useRef(null);\n  const activatedRef = React.useRef(false);\n  React.useEffect(() => {\n    if (!active) {\n      return undefined;\n    }\n\n    // Ensure that this hook is not \"activated\" synchronously.\n    // https://github.com/facebook/react/issues/20074\n    function armClickAwayListener() {\n      activatedRef.current = true;\n    }\n    document.addEventListener('mousedown', armClickAwayListener, true);\n    document.addEventListener('touchstart', armClickAwayListener, true);\n    return () => {\n      document.removeEventListener('mousedown', armClickAwayListener, true);\n      document.removeEventListener('touchstart', armClickAwayListener, true);\n      activatedRef.current = false;\n    };\n  }, [active]);\n\n  // The handler doesn't take event.defaultPrevented into account:\n  //\n  // event.preventDefault() is meant to stop default behaviors like\n  // clicking a checkbox to check it, hitting a button to submit a form,\n  // and hitting left arrow to move the cursor in a text input etc.\n  // Only special HTML elements have these default behaviors.\n  const handleClickAway = useEventCallback(event => {\n    if (!activatedRef.current) {\n      return;\n    }\n\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = syntheticEventRef.current;\n    syntheticEventRef.current = false;\n    const doc = ownerDocument(nodeRef.current);\n\n    // 1. IE11 support, which trigger the handleClickAway even after the unbind\n    // 2. The child might render null.\n    // 3. Behave like a blur listener.\n    if (!nodeRef.current ||\n    // is a TouchEvent?\n    'clientX' in event && clickedRootScrollbar(event, doc)) {\n      return;\n    }\n\n    // Do not act if user performed touchmove\n    if (movedRef.current) {\n      movedRef.current = false;\n      return;\n    }\n    let insideDOM;\n\n    // If not enough, can use https://github.com/DieterHolvoet/event-propagation-path/blob/master/propagationPath.js\n    if (event.composedPath) {\n      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;\n    } else {\n      insideDOM = !doc.documentElement.contains(event.target) || nodeRef.current.contains(event.target);\n    }\n    if (!insideDOM && !insideReactTree) {\n      onClickAway(event);\n    }\n  });\n\n  // Keep track of mouse/touch events that bubbled up through the portal.\n  const handleSynthetic = () => {\n    syntheticEventRef.current = true;\n  };\n  React.useEffect(() => {\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      const handleTouchMove = () => {\n        movedRef.current = true;\n      };\n      doc.addEventListener('touchstart', handleClickAway);\n      doc.addEventListener('touchmove', handleTouchMove);\n      return () => {\n        doc.removeEventListener('touchstart', handleClickAway);\n        doc.removeEventListener('touchmove', handleTouchMove);\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  React.useEffect(() => {\n    // TODO This behavior is not tested automatically\n    // It's unclear whether this is due to different update semantics in test (batched in act() vs discrete on click).\n    // Or if this is a timing related issues due to different Transition components\n    // Once we get rid of all the manual scheduling (for example setTimeout(update, 0)) we can revisit this code+test.\n    if (active) {\n      const doc = ownerDocument(nodeRef.current);\n      doc.addEventListener('click', handleClickAway);\n      return () => {\n        doc.removeEventListener('click', handleClickAway);\n        // cleanup `handleClickAway`\n        syntheticEventRef.current = false;\n      };\n    }\n    return undefined;\n  }, [active, handleClickAway]);\n  return [nodeRef, handleSynthetic, handleSynthetic];\n}\nconst PickerPopperPaperWrapper = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      PaperComponent,\n      ownerState,\n      children,\n      paperSlotProps,\n      paperClasses,\n      onPaperClick,\n      onPaperTouchStart\n      // picks up the style props provided by `Transition`\n      // https://mui.com/material-ui/transitions/#child-requirement\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const paperProps = useSlotProps({\n    elementType: PaperComponent,\n    externalSlotProps: paperSlotProps,\n    additionalProps: {\n      tabIndex: -1,\n      elevation: 8,\n      ref\n    },\n    className: paperClasses,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(PaperComponent, _extends({}, other, paperProps, {\n    onClick: event => {\n      onPaperClick(event);\n      paperProps.onClick?.(event);\n    },\n    onTouchStart: event => {\n      onPaperTouchStart(event);\n      paperProps.onTouchStart?.(event);\n    },\n    ownerState: ownerState,\n    children: children\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") PickerPopperPaperWrapper.displayName = \"PickerPopperPaperWrapper\";\nexport function PickerPopper(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickerPopper'\n  });\n  const {\n    children,\n    placement = 'bottom-start',\n    slots,\n    slotProps,\n    classes: classesProp\n  } = props;\n  const {\n    open,\n    popupRef,\n    reduceAnimations\n  } = usePickerContext();\n  const {\n    dismissViews,\n    getCurrentViewMode,\n    onPopperExited,\n    triggerElement,\n    viewContainerRole\n  } = usePickerPrivateContext();\n  React.useEffect(() => {\n    function handleKeyDown(nativeEvent) {\n      if (open && nativeEvent.key === 'Escape') {\n        dismissViews();\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [dismissViews, open]);\n  const lastFocusedElementRef = React.useRef(null);\n  React.useEffect(() => {\n    if (viewContainerRole === 'tooltip' || getCurrentViewMode() === 'field') {\n      return;\n    }\n    if (open) {\n      lastFocusedElementRef.current = getActiveElement(document);\n    } else if (lastFocusedElementRef.current && lastFocusedElementRef.current instanceof HTMLElement) {\n      // make sure the button is flushed with updated label, before returning focus to it\n      // avoids issue, where screen reader could fail to announce selected date after selection\n      setTimeout(() => {\n        if (lastFocusedElementRef.current instanceof HTMLElement) {\n          lastFocusedElementRef.current.focus();\n        }\n      });\n    }\n  }, [open, viewContainerRole, getCurrentViewMode]);\n  const classes = useUtilityClasses(classesProp);\n  const {\n    ownerState: pickerOwnerState,\n    rootRefObject\n  } = usePickerPrivateContext();\n  const ownerState = _extends({}, pickerOwnerState, {\n    popperPlacement: placement\n  });\n  const handleClickAway = useEventCallback(() => {\n    if (viewContainerRole === 'tooltip') {\n      executeInTheNextEventLoopTick(() => {\n        if (rootRefObject.current?.contains(getActiveElement(document)) || popupRef.current?.contains(getActiveElement(document))) {\n          return;\n        }\n        dismissViews();\n      });\n    } else {\n      dismissViews();\n    }\n  });\n  const [clickAwayRef, onPaperClick, onPaperTouchStart] = useClickAwayListener(open, handleClickAway);\n  const paperRef = React.useRef(null);\n  const handleRef = useForkRef(paperRef, popupRef);\n  const handlePaperRef = useForkRef(handleRef, clickAwayRef);\n  const handleKeyDown = event => {\n    if (event.key === 'Escape') {\n      // stop the propagation to avoid closing parent modal\n      event.stopPropagation();\n      dismissViews();\n    }\n  };\n  const Transition = slots?.desktopTransition ?? reduceAnimations ? Fade : Grow;\n  const FocusTrap = slots?.desktopTrapFocus ?? BaseFocusTrap;\n  const Paper = slots?.desktopPaper ?? PickerPopperPaper;\n  const Popper = slots?.popper ?? PickerPopperRoot;\n  const popperProps = useSlotProps({\n    elementType: Popper,\n    externalSlotProps: slotProps?.popper,\n    additionalProps: {\n      transition: true,\n      role: viewContainerRole == null ? undefined : viewContainerRole,\n      open,\n      placement,\n      anchorEl: triggerElement,\n      onKeyDown: handleKeyDown\n    },\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Popper, _extends({}, popperProps, {\n    children: ({\n      TransitionProps\n    }) => /*#__PURE__*/_jsx(FocusTrap, _extends({\n      open: open,\n      disableAutoFocus: true\n      // pickers are managing focus position manually\n      // without this prop the focus is returned to the button before `aria-label` is updated\n      // which would force screen readers to read too old label\n      ,\n      disableRestoreFocus: true,\n      disableEnforceFocus: viewContainerRole === 'tooltip',\n      isEnabled: () => true\n    }, slotProps?.desktopTrapFocus, {\n      children: /*#__PURE__*/_jsx(Transition, _extends({}, TransitionProps, slotProps?.desktopTransition, {\n        onExited: event => {\n          onPopperExited?.();\n          slotProps?.desktopTransition?.onExited?.(event);\n          TransitionProps?.onExited?.();\n        },\n        children: /*#__PURE__*/_jsx(PickerPopperPaperWrapper, {\n          PaperComponent: Paper,\n          ownerState: ownerState,\n          ref: handlePaperRef,\n          onPaperClick: onPaperClick,\n          onPaperTouchStart: onPaperTouchStart,\n          paperClasses: classes.paper,\n          paperSlotProps: slotProps?.desktopPaper,\n          children: children\n        })\n      }))\n    }))\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,mBAAmB,CAAC;AACrI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,uBAAuB,IAAIC,cAAc,QAAQ,YAAY;AACjM,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,2BAA2B,QAAQ,0BAA0B;AACtE,SAASC,6BAA6B,EAAEC,gBAAgB,QAAQ,sBAAsB;AACtF,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,OAAO,IAAI;EACnC,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAOd,cAAc,CAACY,KAAK,EAAET,2BAA2B,EAAEQ,OAAO,CAAC;AACpE,CAAC;AACD,MAAMI,gBAAgB,GAAGd,MAAM,CAACV,SAAS,EAAE;EACzCyB,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC;EACFC;AACF,CAAC,MAAM;EACLC,MAAM,EAAED,KAAK,CAACC,MAAM,CAACC;AACvB,CAAC,CAAC,CAAC;AACH,MAAMC,iBAAiB,GAAGpB,MAAM,CAACX,QAAQ,EAAE;EACzC0B,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDK,OAAO,EAAE,CAAC;EACVC,eAAe,EAAE,YAAY;EAC7BC,QAAQ,EAAE,CAAC;IACTC,KAAK,EAAEA,CAAC;MACNC;IACF,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAACC,QAAQ,CAACD,eAAe,CAAC;IAC/DE,KAAK,EAAE;MACLL,eAAe,EAAE;IACnB;EACF,CAAC;AACH,CAAC,CAAC;AACF,SAASM,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxC,OAAOA,GAAG,CAACC,eAAe,CAACC,WAAW,GAAGH,KAAK,CAACI,OAAO,IAAIH,GAAG,CAACC,eAAe,CAACG,YAAY,GAAGL,KAAK,CAACM,OAAO;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,WAAW,EAAE;EACjD,MAAMC,QAAQ,GAAGtD,KAAK,CAACuD,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMC,iBAAiB,GAAGxD,KAAK,CAACuD,MAAM,CAAC,KAAK,CAAC;EAC7C,MAAME,OAAO,GAAGzD,KAAK,CAACuD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMG,YAAY,GAAG1D,KAAK,CAACuD,MAAM,CAAC,KAAK,CAAC;EACxCvD,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,IAAI,CAACP,MAAM,EAAE;MACX,OAAOQ,SAAS;IAClB;;IAEA;IACA;IACA,SAASC,oBAAoBA,CAAA,EAAG;MAC9BH,YAAY,CAACI,OAAO,GAAG,IAAI;IAC7B;IACAC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEH,oBAAoB,EAAE,IAAI,CAAC;IAClEE,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEH,oBAAoB,EAAE,IAAI,CAAC;IACnE,OAAO,MAAM;MACXE,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEJ,oBAAoB,EAAE,IAAI,CAAC;MACrEE,QAAQ,CAACE,mBAAmB,CAAC,YAAY,EAAEJ,oBAAoB,EAAE,IAAI,CAAC;MACtEH,YAAY,CAACI,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;;EAEZ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMc,eAAe,GAAGxD,gBAAgB,CAACkC,KAAK,IAAI;IAChD,IAAI,CAACc,YAAY,CAACI,OAAO,EAAE;MACzB;IACF;;IAEA;IACA;IACA,MAAMK,eAAe,GAAGX,iBAAiB,CAACM,OAAO;IACjDN,iBAAiB,CAACM,OAAO,GAAG,KAAK;IACjC,MAAMjB,GAAG,GAAGjC,aAAa,CAAC6C,OAAO,CAACK,OAAO,CAAC;;IAE1C;IACA;IACA;IACA,IAAI,CAACL,OAAO,CAACK,OAAO;IACpB;IACA,SAAS,IAAIlB,KAAK,IAAID,oBAAoB,CAACC,KAAK,EAAEC,GAAG,CAAC,EAAE;MACtD;IACF;;IAEA;IACA,IAAIS,QAAQ,CAACQ,OAAO,EAAE;MACpBR,QAAQ,CAACQ,OAAO,GAAG,KAAK;MACxB;IACF;IACA,IAAIM,SAAS;;IAEb;IACA,IAAIxB,KAAK,CAACyB,YAAY,EAAE;MACtBD,SAAS,GAAGxB,KAAK,CAACyB,YAAY,CAAC,CAAC,CAACC,OAAO,CAACb,OAAO,CAACK,OAAO,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC,MAAM;MACLM,SAAS,GAAG,CAACvB,GAAG,CAACC,eAAe,CAACyB,QAAQ,CAAC3B,KAAK,CAAC4B,MAAM,CAAC,IAAIf,OAAO,CAACK,OAAO,CAACS,QAAQ,CAAC3B,KAAK,CAAC4B,MAAM,CAAC;IACnG;IACA,IAAI,CAACJ,SAAS,IAAI,CAACD,eAAe,EAAE;MAClCd,WAAW,CAACT,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;;EAEF;EACA,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5BjB,iBAAiB,CAACM,OAAO,GAAG,IAAI;EAClC,CAAC;EACD9D,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,IAAIP,MAAM,EAAE;MACV,MAAMP,GAAG,GAAGjC,aAAa,CAAC6C,OAAO,CAACK,OAAO,CAAC;MAC1C,MAAMY,eAAe,GAAGA,CAAA,KAAM;QAC5BpB,QAAQ,CAACQ,OAAO,GAAG,IAAI;MACzB,CAAC;MACDjB,GAAG,CAACmB,gBAAgB,CAAC,YAAY,EAAEE,eAAe,CAAC;MACnDrB,GAAG,CAACmB,gBAAgB,CAAC,WAAW,EAAEU,eAAe,CAAC;MAClD,OAAO,MAAM;QACX7B,GAAG,CAACoB,mBAAmB,CAAC,YAAY,EAAEC,eAAe,CAAC;QACtDrB,GAAG,CAACoB,mBAAmB,CAAC,WAAW,EAAES,eAAe,CAAC;MACvD,CAAC;IACH;IACA,OAAOd,SAAS;EAClB,CAAC,EAAE,CAACR,MAAM,EAAEc,eAAe,CAAC,CAAC;EAC7BlE,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB;IACA;IACA;IACA;IACA,IAAIP,MAAM,EAAE;MACV,MAAMP,GAAG,GAAGjC,aAAa,CAAC6C,OAAO,CAACK,OAAO,CAAC;MAC1CjB,GAAG,CAACmB,gBAAgB,CAAC,OAAO,EAAEE,eAAe,CAAC;MAC9C,OAAO,MAAM;QACXrB,GAAG,CAACoB,mBAAmB,CAAC,OAAO,EAAEC,eAAe,CAAC;QACjD;QACAV,iBAAiB,CAACM,OAAO,GAAG,KAAK;MACnC,CAAC;IACH;IACA,OAAOF,SAAS;EAClB,CAAC,EAAE,CAACR,MAAM,EAAEc,eAAe,CAAC,CAAC;EAC7B,OAAO,CAACT,OAAO,EAAEgB,eAAe,EAAEA,eAAe,CAAC;AACpD;AACA,MAAME,wBAAwB,GAAG,aAAa3E,KAAK,CAAC4E,UAAU,CAAC,CAACrC,KAAK,EAAEsC,GAAG,KAAK;EAC7E,MAAM;MACFC,cAAc;MACdC,UAAU;MACVC,QAAQ;MACRC,cAAc;MACdC,YAAY;MACZC,YAAY;MACZC;MACA;MACA;IACF,CAAC,GAAG7C,KAAK;IACT8C,KAAK,GAAGvF,6BAA6B,CAACyC,KAAK,EAAExC,SAAS,CAAC;EACzD,MAAMuF,UAAU,GAAGrF,YAAY,CAAC;IAC9BsF,WAAW,EAAET,cAAc;IAC3BU,iBAAiB,EAAEP,cAAc;IACjCQ,eAAe,EAAE;MACfC,QAAQ,EAAE,CAAC,CAAC;MACZC,SAAS,EAAE,CAAC;MACZd;IACF,CAAC;IACDe,SAAS,EAAEV,YAAY;IACvBH;EACF,CAAC,CAAC;EACF,OAAO,aAAaxD,IAAI,CAACuD,cAAc,EAAEjF,QAAQ,CAAC,CAAC,CAAC,EAAEwF,KAAK,EAAEC,UAAU,EAAE;IACvEO,OAAO,EAAEjD,KAAK,IAAI;MAChBuC,YAAY,CAACvC,KAAK,CAAC;MACnB0C,UAAU,CAACO,OAAO,GAAGjD,KAAK,CAAC;IAC7B,CAAC;IACDkD,YAAY,EAAElD,KAAK,IAAI;MACrBwC,iBAAiB,CAACxC,KAAK,CAAC;MACxB0C,UAAU,CAACQ,YAAY,GAAGlD,KAAK,CAAC;IAClC,CAAC;IACDmC,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEtB,wBAAwB,CAACuB,WAAW,GAAG,0BAA0B;AAC5G,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,MAAM7D,KAAK,GAAGvB,aAAa,CAAC;IAC1BuB,KAAK,EAAE6D,OAAO;IACdtE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJkD,QAAQ;IACRqB,SAAS,GAAG,cAAc;IAC1B3E,KAAK;IACL4E,SAAS;IACT7E,OAAO,EAAE8E;EACX,CAAC,GAAGhE,KAAK;EACT,MAAM;IACJiE,IAAI;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGrF,gBAAgB,CAAC,CAAC;EACtB,MAAM;IACJsF,YAAY;IACZC,kBAAkB;IAClBC,cAAc;IACdC,cAAc;IACdC;EACF,CAAC,GAAG3F,uBAAuB,CAAC,CAAC;EAC7BpB,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,SAASqD,aAAaA,CAACC,WAAW,EAAE;MAClC,IAAIT,IAAI,IAAIS,WAAW,CAACC,GAAG,KAAK,QAAQ,EAAE;QACxCP,YAAY,CAAC,CAAC;MAChB;IACF;IACA5C,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEgD,aAAa,CAAC;IACnD,OAAO,MAAM;MACXjD,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAE+C,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACL,YAAY,EAAEH,IAAI,CAAC,CAAC;EACxB,MAAMW,qBAAqB,GAAGnH,KAAK,CAACuD,MAAM,CAAC,IAAI,CAAC;EAChDvD,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,IAAIoD,iBAAiB,KAAK,SAAS,IAAIH,kBAAkB,CAAC,CAAC,KAAK,OAAO,EAAE;MACvE;IACF;IACA,IAAIJ,IAAI,EAAE;MACRW,qBAAqB,CAACrD,OAAO,GAAG3C,gBAAgB,CAAC4C,QAAQ,CAAC;IAC5D,CAAC,MAAM,IAAIoD,qBAAqB,CAACrD,OAAO,IAAIqD,qBAAqB,CAACrD,OAAO,YAAYsD,WAAW,EAAE;MAChG;MACA;MACAC,UAAU,CAAC,MAAM;QACf,IAAIF,qBAAqB,CAACrD,OAAO,YAAYsD,WAAW,EAAE;UACxDD,qBAAqB,CAACrD,OAAO,CAACwD,KAAK,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACd,IAAI,EAAEO,iBAAiB,EAAEH,kBAAkB,CAAC,CAAC;EACjD,MAAMnF,OAAO,GAAGD,iBAAiB,CAAC+E,WAAW,CAAC;EAC9C,MAAM;IACJxB,UAAU,EAAEwC,gBAAgB;IAC5BC;EACF,CAAC,GAAGpG,uBAAuB,CAAC,CAAC;EAC7B,MAAM2D,UAAU,GAAGlF,QAAQ,CAAC,CAAC,CAAC,EAAE0H,gBAAgB,EAAE;IAChD/E,eAAe,EAAE6D;EACnB,CAAC,CAAC;EACF,MAAMnC,eAAe,GAAGxD,gBAAgB,CAAC,MAAM;IAC7C,IAAIqG,iBAAiB,KAAK,SAAS,EAAE;MACnC7F,6BAA6B,CAAC,MAAM;QAClC,IAAIsG,aAAa,CAAC1D,OAAO,EAAES,QAAQ,CAACpD,gBAAgB,CAAC4C,QAAQ,CAAC,CAAC,IAAI0C,QAAQ,CAAC3C,OAAO,EAAES,QAAQ,CAACpD,gBAAgB,CAAC4C,QAAQ,CAAC,CAAC,EAAE;UACzH;QACF;QACA4C,YAAY,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;EACF,MAAM,CAACc,YAAY,EAAEtC,YAAY,EAAEC,iBAAiB,CAAC,GAAGjC,oBAAoB,CAACqD,IAAI,EAAEtC,eAAe,CAAC;EACnG,MAAMwD,QAAQ,GAAG1H,KAAK,CAACuD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMoE,SAAS,GAAGnH,UAAU,CAACkH,QAAQ,EAAEjB,QAAQ,CAAC;EAChD,MAAMmB,cAAc,GAAGpH,UAAU,CAACmH,SAAS,EAAEF,YAAY,CAAC;EAC1D,MAAMT,aAAa,GAAGpE,KAAK,IAAI;IAC7B,IAAIA,KAAK,CAACsE,GAAG,KAAK,QAAQ,EAAE;MAC1B;MACAtE,KAAK,CAACiF,eAAe,CAAC,CAAC;MACvBlB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EACD,MAAMmB,UAAU,GAAGpG,KAAK,EAAEqG,iBAAiB,IAAIrB,gBAAgB,GAAGvG,IAAI,GAAGD,IAAI;EAC7E,MAAM8H,SAAS,GAAGtG,KAAK,EAAEuG,gBAAgB,IAAI3H,aAAa;EAC1D,MAAM4H,KAAK,GAAGxG,KAAK,EAAEyG,YAAY,IAAIhG,iBAAiB;EACtD,MAAMiG,MAAM,GAAG1G,KAAK,EAAE2G,MAAM,IAAIxG,gBAAgB;EAChD,MAAMyG,WAAW,GAAGrI,YAAY,CAAC;IAC/BsF,WAAW,EAAE6C,MAAM;IACnB5C,iBAAiB,EAAEc,SAAS,EAAE+B,MAAM;IACpC5C,eAAe,EAAE;MACf8C,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEzB,iBAAiB,IAAI,IAAI,GAAGnD,SAAS,GAAGmD,iBAAiB;MAC/DP,IAAI;MACJH,SAAS;MACToC,QAAQ,EAAE3B,cAAc;MACxB4B,SAAS,EAAE1B;IACb,CAAC;IACDpB,SAAS,EAAEnE,OAAO,CAACE,IAAI;IACvBoD;EACF,CAAC,CAAC;EACF,OAAO,aAAaxD,IAAI,CAAC6G,MAAM,EAAEvI,QAAQ,CAAC,CAAC,CAAC,EAAEyI,WAAW,EAAE;IACzDtD,QAAQ,EAAEA,CAAC;MACT2D;IACF,CAAC,KAAK,aAAapH,IAAI,CAACyG,SAAS,EAAEnI,QAAQ,CAAC;MAC1C2G,IAAI,EAAEA,IAAI;MACVoC,gBAAgB,EAAE;MAClB;MACA;MACA;MAAA;;MAEAC,mBAAmB,EAAE,IAAI;MACzBC,mBAAmB,EAAE/B,iBAAiB,KAAK,SAAS;MACpDgC,SAAS,EAAEA,CAAA,KAAM;IACnB,CAAC,EAAEzC,SAAS,EAAE2B,gBAAgB,EAAE;MAC9BjD,QAAQ,EAAE,aAAazD,IAAI,CAACuG,UAAU,EAAEjI,QAAQ,CAAC,CAAC,CAAC,EAAE8I,eAAe,EAAErC,SAAS,EAAEyB,iBAAiB,EAAE;QAClGiB,QAAQ,EAAEpG,KAAK,IAAI;UACjBiE,cAAc,GAAG,CAAC;UAClBP,SAAS,EAAEyB,iBAAiB,EAAEiB,QAAQ,GAAGpG,KAAK,CAAC;UAC/C+F,eAAe,EAAEK,QAAQ,GAAG,CAAC;QAC/B,CAAC;QACDhE,QAAQ,EAAE,aAAazD,IAAI,CAACoD,wBAAwB,EAAE;UACpDG,cAAc,EAAEoD,KAAK;UACrBnD,UAAU,EAAEA,UAAU;UACtBF,GAAG,EAAE+C,cAAc;UACnBzC,YAAY,EAAEA,YAAY;UAC1BC,iBAAiB,EAAEA,iBAAiB;UACpCF,YAAY,EAAEzD,OAAO,CAACG,KAAK;UAC3BqD,cAAc,EAAEqB,SAAS,EAAE6B,YAAY;UACvCnD,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}