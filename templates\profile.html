{% extends 'base.html' %}
{% load static %}

{% block title %}الملف الشخصي - نظام إدارة الصيدليات{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">الملف الشخصي</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>
        الملف الشخصي
    </h1>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-4">
        <!-- Profile Card -->
        <div class="card mb-4">
            <div class="card-body text-center">
                <div class="mb-3">
                    <div class="avatar-circle mx-auto mb-3">
                        <i class="fas fa-user fa-3x"></i>
                    </div>
                    <h5 class="card-title">{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-muted">{{ user.get_role_display|default:"مستخدم" }}</p>
                </div>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="mb-0">{{ user.branch.name|default:"غير محدد" }}</h6>
                            <small class="text-muted">الفرع</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="mb-0">{{ user.date_joined|date:"Y/m/d" }}</h6>
                            <small class="text-muted">تاريخ الانضمام</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="mb-0">{{ user.last_login|date:"Y/m/d" }}</h6>
                        <small class="text-muted">آخر دخول</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>مبيعات اليوم</span>
                    <span class="badge bg-primary">{{ today_sales|default:0 }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>إجمالي المبيعات</span>
                    <span class="badge bg-success">{{ total_sales|default:0 }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>آخر نشاط</span>
                    <span class="text-muted">{{ user.last_login|timesince }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <!-- Personal Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="{{ user.first_name }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">الاسم الأخير</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="{{ user.last_name }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ user.email }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ user.phone|default:'' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="branch" class="form-label">الفرع</label>
                            <input type="text" class="form-control" id="branch" 
                                   value="{{ user.branch.name|default:'غير محدد' }}" readonly>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="2">{{ user.address|default:'' }}</textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ التغييرات
                    </button>
                </form>
            </div>
        </div>

        <!-- Change Password -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lock me-2"></i>
                    تغيير كلمة المرور
                </h5>
            </div>
            <div class="card-body">
                <form id="changePasswordForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="current_password" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="confirm_password" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </button>
                </form>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    سجل النشاط
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">تسجيل دخول</h6>
                            <p class="timeline-text">تم تسجيل الدخول بنجاح</p>
                            <small class="text-muted">{{ user.last_login|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">تحديث الملف الشخصي</h6>
                            <p class="timeline-text">تم تحديث المعلومات الشخصية</p>
                            <small class="text-muted">منذ يومين</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">إنشاء الحساب</h6>
                            <p class="timeline-text">تم إنشاء الحساب بنجاح</p>
                            <small class="text-muted">{{ user.date_joined|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -37px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-text {
    margin-bottom: 5px;
    color: #6c757d;
}

.border-end {
    border-left: 1px solid #dee2e6 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// تغيير كلمة المرور
document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (newPassword !== confirmPassword) {
        alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
        return;
    }
    
    if (newPassword.length < 8) {
        alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
        return;
    }
    
    // هنا يمكن إرسال طلب AJAX لتغيير كلمة المرور
    alert('سيتم إضافة هذه الميزة قريباً');
});

// تحديث الصفحة كل 5 دقائق لتحديث آخر نشاط
setInterval(function() {
    // تحديث وقت آخر نشاط
    const lastActivityElement = document.querySelector('.text-muted:last-child');
    if (lastActivityElement) {
        // يمكن إضافة كود لتحديث الوقت
    }
}, 300000);
</script>
{% endblock %}
