# Generated by Django 5.2.1 on 2025-05-26 12:07

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('branches', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المورد')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المورد')),
                ('contact_person', models.CharField(max_length=100, verbose_name='الشخص المسؤول')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('country', models.CharField(max_length=50, verbose_name='البلد')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='حد الائتمان')),
                ('payment_terms', models.CharField(blank=True, max_length=100, verbose_name='شروط الدفع')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردون',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الطلب')),
                ('order_date', models.DateField(verbose_name='تاريخ الطلب')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('actual_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم الفعلي')),
                ('subtotal', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المجموع الفرعي')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='مبلغ الخصم')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='مبلغ الضريبة')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='تكلفة الشحن')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='المبلغ المدفوع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسل'), ('confirmed', 'مؤكد'), ('received', 'مستلم'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('payment_status', models.CharField(choices=[('pending', 'معلق'), ('partial', 'جزئي'), ('paid', 'مدفوع')], default='pending', max_length=20, verbose_name='حالة الدفع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_orders', to='branches.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_purchase_orders', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_orders', to='suppliers.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'أمر شراء',
                'verbose_name_plural': 'أوامر الشراء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_ordered', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية المطلوبة')),
                ('quantity_received', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المستلمة')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='التكلفة الإجمالية')),
                ('batch_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الدفعة')),
                ('manufacturing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التصنيع')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('medicine', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.medicine', verbose_name='الدواء')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='suppliers.purchaseorder', verbose_name='أمر الشراء')),
            ],
            options={
                'verbose_name': 'عنصر أمر الشراء',
                'verbose_name_plural': 'عناصر أمر الشراء',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الدفعة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان')], max_length=20, verbose_name='طريقة الدفع')),
                ('payment_date', models.DateField(verbose_name='تاريخ الدفع')),
                ('reference_number', models.CharField(blank=True, max_length=50, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('processed_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='suppliers.purchaseorder', verbose_name='أمر الشراء')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='suppliers.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='SupplierOffer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان العرض')),
                ('description', models.TextField(verbose_name='وصف العرض')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='نسبة الخصم')),
                ('minimum_quantity', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)], verbose_name='الحد الأدنى للكمية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offers', to='suppliers.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'عرض مورد',
                'verbose_name_plural': 'عروض الموردين',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='SupplierOfferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('special_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='السعر الخاص')),
                ('medicine', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.medicine', verbose_name='الدواء')),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='suppliers.supplieroffer', verbose_name='العرض')),
            ],
            options={
                'verbose_name': 'عنصر عرض المورد',
                'verbose_name_plural': 'عناصر عروض الموردين',
                'unique_together': {('offer', 'medicine')},
            },
        ),
    ]
