{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام إدارة الصيدليات{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
        <button type="button" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i>
            بيع جديد
        </button>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.total_sales|default:0 }}</div>
                    <div class="stats-label">إجمالي المبيعات اليوم</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-cash-register fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.total_revenue|default:0 }}</div>
                    <div class="stats-label">الإيرادات (ر.س)</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-chart-line fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.total_customers|default:0 }}</div>
                    <div class="stats-label">العملاء</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ stats.low_stock_items|default:0 }}</div>
                    <div class="stats-label">أدوية منخفضة المخزون</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'pos:new_sale' %}" class="btn btn-outline-primary w-100 p-3">
                            <i class="fas fa-cash-register fa-2x mb-2"></i><br>
                            بيع جديد
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'inventory:add_medicine' %}" class="btn btn-outline-success w-100 p-3">
                            <i class="fas fa-pills fa-2x mb-2"></i><br>
                            إضافة دواء
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'customers:add_customer' %}" class="btn btn-outline-info w-100 p-3">
                            <i class="fas fa-user-plus fa-2x mb-2"></i><br>
                            عميل جديد
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'suppliers:add_purchase_order' %}" class="btn btn-outline-warning w-100 p-3">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                            أمر شراء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    <!-- Sales Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    مبيعات آخر 7 أيام
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Recent Sales -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    آخر المبيعات
                </h5>
            </div>
            <div class="card-body">
                {% for sale in recent_sales %}
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">فاتورة #{{ sale.sale_number }}</h6>
                        <small class="text-muted">{{ sale.created_at|date:"H:i" }}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success">{{ sale.total_amount }} ر.س</span>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد مبيعات حديثة</p>
                {% endfor %}
                
                <div class="text-center mt-3">
                    <a href="{% url 'pos:sale_list' %}" class="btn btn-sm btn-outline-primary">
                        عرض جميع المبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts and Notifications -->
<div class="row">
    <!-- Low Stock Alerts -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                    تنبيهات المخزون
                </h5>
            </div>
            <div class="card-body">
                {% for alert in stock_alerts %}
                <div class="alert alert-warning d-flex align-items-center mb-2">
                    <i class="fas fa-pills me-2"></i>
                    <div class="flex-grow-1">
                        <strong>{{ alert.medicine.name }}</strong><br>
                        <small>المخزون الحالي: {{ alert.current_stock }}</small>
                    </div>
                    <span class="badge bg-warning">{{ alert.get_alert_type_display }}</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد تنبيهات مخزون</p>
                {% endfor %}
                
                {% if stock_alerts %}
                <div class="text-center mt-3">
                    <a href="{% url 'inventory:stock_alerts' %}" class="btn btn-sm btn-outline-warning">
                        عرض جميع التنبيهات
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Expiry Alerts -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-times me-2 text-danger"></i>
                    تنبيهات انتهاء الصلاحية
                </h5>
            </div>
            <div class="card-body">
                {% for batch in expiring_batches %}
                <div class="alert alert-danger d-flex align-items-center mb-2">
                    <i class="fas fa-calendar-times me-2"></i>
                    <div class="flex-grow-1">
                        <strong>{{ batch.medicine.name }}</strong><br>
                        <small>ينتهي في: {{ batch.expiry_date|date:"Y/m/d" }}</small>
                    </div>
                    <span class="badge bg-danger">{{ batch.days_to_expiry }} يوم</span>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد أدوية قاربت على الانتهاء</p>
                {% endfor %}
                
                {% if expiring_batches %}
                <div class="text-center mt-3">
                    <a href="{% url 'inventory:expiring_medicines' %}" class="btn btn-sm btn-outline-danger">
                        عرض جميع الأدوية المنتهية
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Top Selling Medicines -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    أكثر الأدوية مبيعاً
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>الفئة</th>
                                <th>الكمية المباعة</th>
                                <th>الإيرادات</th>
                                <th>المخزون الحالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in top_selling %}
                            <tr>
                                <td>
                                    <strong>{{ item.medicine.name }}</strong><br>
                                    <small class="text-muted">{{ item.medicine.generic_name }}</small>
                                </td>
                                <td>{{ item.medicine.category.name }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ item.total_quantity }}</span>
                                </td>
                                <td>{{ item.total_revenue }} ر.س</td>
                                <td>
                                    {% if item.medicine.current_stock < item.medicine.minimum_stock %}
                                        <span class="badge bg-danger">{{ item.medicine.current_stock }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ item.medicine.current_stock }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">لا توجد بيانات مبيعات</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Sales Chart
const ctx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {{ sales_chart_labels|safe }},
        datasets: [{
            label: 'المبيعات (ر.س)',
            data: {{ sales_chart_data|safe }},
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + ' ر.س';
                    }
                }
            }
        }
    }
});

// Auto refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
