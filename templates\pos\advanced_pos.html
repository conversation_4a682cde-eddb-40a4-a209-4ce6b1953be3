{% extends 'base.html' %}
{% load static %}

{% block title %}نقطة البيع المتقدمة - نظام إدارة الصيدليات{% endblock %}

{% block extra_css %}
<style>
    .pos-container {
        height: calc(100vh - 120px);
        overflow: hidden;
    }

    .product-search {
        position: sticky;
        top: 0;
        z-index: 100;
        background: white;
        padding: 15px 0;
        border-bottom: 2px solid #e9ecef;
    }

    .search-input {
        font-size: 1.1rem;
        padding: 12px 20px;
        border-radius: 25px;
        border: 2px solid #667eea;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        border-color: #764ba2;
        box-shadow: 0 0 0 0.2rem rgba(118, 75, 162, 0.25);
        transform: scale(1.02);
    }

    .products-grid {
        height: calc(100vh - 250px);
        overflow-y: auto;
        padding: 20px 0;
    }

    .product-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 180px;
        position: relative;
        overflow: hidden;
    }

    .product-card:hover {
        border-color: #667eea;
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
    }

    .product-card.selected {
        border-color: #28a745;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .product-card .card-body {
        padding: 15px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
    }

    .product-name {
        font-weight: 600;
        font-size: 0.95rem;
        line-height: 1.3;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .product-price {
        font-size: 1.1rem;
        font-weight: 700;
        color: #28a745;
    }

    .product-card.selected .product-price {
        color: white;
    }

    .product-stock {
        font-size: 0.85rem;
        opacity: 0.8;
    }

    .product-stock.low {
        color: #dc3545;
        font-weight: 600;
    }

    .cart-container {
        height: calc(100vh - 120px);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px 0 0 20px;
        color: white;
        display: flex;
        flex-direction: column;
    }

    .cart-header {
        padding: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .cart-items {
        flex: 1;
        overflow-y: auto;
        padding: 0 20px;
    }

    .cart-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .cart-item-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 10px 0;
    }

    .quantity-btn {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        border: 2px solid white;
        background: transparent;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .quantity-btn:hover {
        background: white;
        color: #667eea;
    }

    .quantity-input {
        width: 60px;
        text-align: center;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        padding: 5px;
    }

    .quantity-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .cart-summary {
        padding: 20px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(0, 0, 0, 0.1);
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        margin: 8px 0;
        font-size: 1.1rem;
    }

    .summary-row.total {
        font-size: 1.3rem;
        font-weight: 700;
        border-top: 2px solid rgba(255, 255, 255, 0.3);
        padding-top: 10px;
        margin-top: 15px;
    }

    .payment-section {
        margin-top: 20px;
    }

    .payment-methods {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin: 15px 0;
    }

    .payment-method {
        padding: 12px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        background: transparent;
        color: white;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .payment-method:hover,
    .payment-method.active {
        background: rgba(255, 255, 255, 0.2);
        border-color: white;
    }

    .complete-sale-btn {
        width: 100%;
        padding: 15px;
        font-size: 1.2rem;
        font-weight: 700;
        background: #28a745;
        border: none;
        border-radius: 15px;
        color: white;
        transition: all 0.3s ease;
        margin-top: 15px;
    }

    .complete-sale-btn:hover {
        background: #218838;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }

    .complete-sale-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .customer-section {
        margin: 15px 0;
        padding: 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
    }

    .customer-search {
        width: 100%;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        margin-bottom: 10px;
    }

    .customer-search::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .quick-actions {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .quick-action-btn {
        flex: 1;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .quick-action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: white;
    }

    .empty-cart {
        text-align: center;
        padding: 40px 20px;
        opacity: 0.7;
    }

    .empty-cart i {
        font-size: 3rem;
        margin-bottom: 15px;
    }

    @media (max-width: 768px) {
        .pos-container {
            height: auto;
        }

        .cart-container {
            border-radius: 20px;
            margin-top: 20px;
        }

        .products-grid {
            height: auto;
            max-height: 400px;
        }
    }
</style>
{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'pos:sale_list' %}">نقاط البيع</a></li>
<li class="breadcrumb-item active">نقطة البيع المتقدمة</li>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="pos-container">
    <div class="row h-100">
        <!-- Products Section -->
        <div class="col-lg-8 col-md-7">
            <!-- Search Bar -->
            <div class="product-search">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="position-relative">
                            <input type="text"
                                   class="form-control search-input"
                                   id="productSearch"
                                   placeholder="ابحث عن دواء بالاسم، الباركود، أو المادة الفعالة..."
                                   autocomplete="off">
                            <i class="fas fa-search position-absolute"
                               style="left: 20px; top: 50%; transform: translateY(-50%); color: #667eea;"></i>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="quick-actions">
                            <button class="quick-action-btn" onclick="scanBarcode()">
                                <i class="fas fa-barcode me-1"></i>
                                مسح باركود
                            </button>
                            <button class="quick-action-btn" onclick="showCategories()">
                                <i class="fas fa-th-large me-1"></i>
                                الفئات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-grid">
                <div class="row" id="productsContainer">
                    <!-- Products will be loaded here -->
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">ابحث عن دواء لبدء البيع</h5>
                        <p class="text-muted">استخدم شريط البحث أعلاه للعثور على الأدوية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Section -->
        <div class="col-lg-4 col-md-5">
            <div class="cart-container">
                <!-- Cart Header -->
                <div class="cart-header">
                    <h4 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        سلة التسوق
                        <span class="badge bg-light text-dark ms-2" id="cartCount">0</span>
                    </h4>
                </div>

                <!-- Customer Section -->
                <div class="customer-section">
                    <h6 class="mb-2">
                        <i class="fas fa-user me-2"></i>
                        العميل
                    </h6>
                    <input type="text"
                           class="customer-search"
                           id="customerSearch"
                           placeholder="ابحث عن عميل أو أضف جديد..."
                           autocomplete="off">
                    <div class="selected-customer d-none" id="selectedCustomer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong id="customerName"></strong><br>
                                <small id="customerPhone"></small>
                            </div>
                            <button class="btn btn-sm btn-outline-light" onclick="clearCustomer()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Cart Items -->
                <div class="cart-items" id="cartItems">
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <h6>السلة فارغة</h6>
                        <p>أضف منتجات لبدء البيع</p>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="cart-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0.00 ر.س</span>
                    </div>
                    <div class="summary-row">
                        <span>الخصم:</span>
                        <span id="discount">0.00 ر.س</span>
                    </div>
                    <div class="summary-row">
                        <span>الضريبة (15%):</span>
                        <span id="tax">0.00 ر.س</span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span id="total">0.00 ر.س</span>
                    </div>

                    <!-- Payment Section -->
                    <div class="payment-section">
                        <h6 class="mb-2">طريقة الدفع</h6>
                        <div class="payment-methods">
                            <button class="payment-method active" data-method="cash">
                                <i class="fas fa-money-bill-wave d-block mb-1"></i>
                                نقدي
                            </button>
                            <button class="payment-method" data-method="card">
                                <i class="fas fa-credit-card d-block mb-1"></i>
                                بطاقة
                            </button>
                            <button class="payment-method" data-method="insurance">
                                <i class="fas fa-shield-alt d-block mb-1"></i>
                                تأمين
                            </button>
                            <button class="payment-method" data-method="mixed">
                                <i class="fas fa-coins d-block mb-1"></i>
                                مختلط
                            </button>
                        </div>

                        <div class="payment-details mt-3">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">المبلغ المدفوع</label>
                                    <input type="number"
                                           class="form-control"
                                           id="paidAmount"
                                           placeholder="0.00"
                                           step="0.01">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">الباقي</label>
                                    <input type="text"
                                           class="form-control"
                                           id="changeAmount"
                                           readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <button class="complete-sale-btn" id="completeSaleBtn" onclick="completeSale()" disabled>
                        <i class="fas fa-check me-2"></i>
                        إتمام البيع
                    </button>

                    <div class="row mt-2">
                        <div class="col-6">
                            <button class="btn btn-outline-light w-100" onclick="saveDraft()">
                                <i class="fas fa-save me-1"></i>
                                حفظ مسودة
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-light w-100" onclick="clearCart()">
                                <i class="fas fa-trash me-1"></i>
                                مسح الكل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Customer Modal -->
<div class="modal fade" id="customerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <div class="mb-3">
                        <label class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="newCustomerName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="newCustomerPhone" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="newCustomerEmail">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addNewCustomer()">إضافة</button>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>تفاصيل البيع</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>المجموع الفرعي:</td>
                                <td class="text-end" id="modalSubtotal">0.00 ر.س</td>
                            </tr>
                            <tr>
                                <td>الخصم:</td>
                                <td class="text-end" id="modalDiscount">0.00 ر.س</td>
                            </tr>
                            <tr>
                                <td>الضريبة:</td>
                                <td class="text-end" id="modalTax">0.00 ر.س</td>
                            </tr>
                            <tr class="table-primary">
                                <td><strong>الإجمالي:</strong></td>
                                <td class="text-end"><strong id="modalTotal">0.00 ر.س</strong></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>تفاصيل الدفع</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>طريقة الدفع:</td>
                                <td class="text-end" id="modalPaymentMethod">نقدي</td>
                            </tr>
                            <tr>
                                <td>المبلغ المدفوع:</td>
                                <td class="text-end" id="modalPaidAmount">0.00 ر.س</td>
                            </tr>
                            <tr>
                                <td>الباقي:</td>
                                <td class="text-end" id="modalChangeAmount">0.00 ر.س</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="mt-3">
                    <label class="form-label">ملاحظات (اختياري)</label>
                    <textarea class="form-control" id="saleNotes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="processSale()">
                    <i class="fas fa-check me-2"></i>
                    تأكيد البيع
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let cart = [];
let selectedCustomer = null;
let selectedPaymentMethod = 'cash';
let searchTimeout = null;

// Initialize POS system
document.addEventListener('DOMContentLoaded', function() {
    initializePOS();
});

function initializePOS() {
    // Setup event listeners
    setupEventListeners();

    // Load initial data
    loadProducts();

    // Setup keyboard shortcuts
    setupKeyboardShortcuts();

    console.log('POS System initialized successfully');
}

function setupEventListeners() {
    // Product search
    document.getElementById('productSearch').addEventListener('input', function(e) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            searchProducts(e.target.value);
        }, 300);
    });

    // Customer search
    document.getElementById('customerSearch').addEventListener('input', function(e) {
        searchCustomers(e.target.value);
    });

    // Payment method selection
    document.querySelectorAll('.payment-method').forEach(btn => {
        btn.addEventListener('click', function() {
            selectPaymentMethod(this.dataset.method);
        });
    });

    // Paid amount calculation
    document.getElementById('paidAmount').addEventListener('input', calculateChange);

    // Enter key on search
    document.getElementById('productSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const firstProduct = document.querySelector('.product-card');
            if (firstProduct) {
                firstProduct.click();
            }
        }
    });
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // F1 - Focus search
        if (e.key === 'F1') {
            e.preventDefault();
            document.getElementById('productSearch').focus();
        }

        // F2 - Focus customer search
        if (e.key === 'F2') {
            e.preventDefault();
            document.getElementById('customerSearch').focus();
        }

        // F3 - Complete sale
        if (e.key === 'F3') {
            e.preventDefault();
            if (!document.getElementById('completeSaleBtn').disabled) {
                completeSale();
            }
        }

        // F4 - Clear cart
        if (e.key === 'F4') {
            e.preventDefault();
            clearCart();
        }

        // Escape - Clear search
        if (e.key === 'Escape') {
            document.getElementById('productSearch').value = '';
            searchProducts('');
        }
    });
}

function loadProducts(query = '') {
    // Show loading state
    showProductsLoading();

    // Make API call
    const url = query ? `/pos/api/search-products/?q=${encodeURIComponent(query)}` : '/pos/api/search-products/';

    fetch(url)
        .then(response => response.json())
        .then(data => {
            displayProducts(data.products || []);
        })
        .catch(error => {
            console.error('Error loading products:', error);
            showAlert('خطأ', 'حدث خطأ في تحميل المنتجات', 'error');
            displayProducts([]);
        });
}

function showProductsLoading() {
    document.getElementById('productsContainer').innerHTML = `
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <h5 class="text-muted mt-3">جاري البحث...</h5>
        </div>
    `;
}

function displayProducts(products) {
    const container = document.getElementById('productsContainer');

    if (products.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج</h5>
                <p class="text-muted">جرب البحث بكلمات مختلفة</p>
            </div>
        `;
        return;
    }

    container.innerHTML = products.map(product => `
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card product-card" onclick="addToCart(${product.id}, '${product.name}', ${product.price}, ${product.stock})">
                <div class="card-body">
                    <div>
                        <h6 class="product-name">${product.name}</h6>
                        <small class="text-muted">${product.generic_name}</small>
                    </div>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="product-price">${product.price.toFixed(2)} ر.س</span>
                            <span class="product-stock ${product.stock < 10 ? 'low' : ''}">
                                <i class="fas fa-boxes me-1"></i>
                                ${product.stock}
                            </span>
                        </div>
                        <small class="text-muted">${product.category}</small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function searchProducts(query) {
    if (query.length < 2) {
        loadProducts();
        return;
    }

    // Simulate search API call
    loadProducts(query);
}

function addToCart(productId, productName, price, stock) {
    // Check if product already in cart
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        if (existingItem.quantity < stock) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            showAlert('تحذير', 'الكمية المطلوبة غير متوفرة في المخزون', 'warning');
            return;
        }
    } else {
        cart.push({
            id: productId,
            name: productName,
            price: price,
            quantity: 1,
            total: price,
            stock: stock
        });
    }

    updateCartDisplay();
    updateCartSummary();

    // Visual feedback
    showProductAdded(productName);
}

function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const cartCount = document.getElementById('cartCount');

    cartCount.textContent = cart.length;

    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <h6>السلة فارغة</h6>
                <p>أضف منتجات لبدء البيع</p>
            </div>
        `;
        return;
    }

    cartItems.innerHTML = cart.map(item => `
        <div class="cart-item">
            <div class="cart-item-name">${item.name}</div>
            <div class="d-flex justify-content-between align-items-center">
                <span>${item.price.toFixed(2)} ر.س</span>
                <span>${item.total.toFixed(2)} ر.س</span>
            </div>
            <div class="quantity-controls">
                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                <input type="number" class="quantity-input" value="${item.quantity}"
                       onchange="updateQuantity(${item.id}, this.value)" min="1" max="${item.stock}">
                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                <button class="btn btn-sm btn-outline-light ms-2" onclick="removeFromCart(${item.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function updateQuantity(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;

    newQuantity = parseInt(newQuantity);

    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }

    if (newQuantity > item.stock) {
        showAlert('تحذير', 'الكمية المطلوبة غير متوفرة في المخزون', 'warning');
        return;
    }

    item.quantity = newQuantity;
    item.total = item.quantity * item.price;

    updateCartDisplay();
    updateCartSummary();
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
    updateCartSummary();
}

function updateCartSummary() {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discount = 0; // Can be calculated based on customer or promotions
    const taxRate = 0.15; // 15% VAT
    const tax = (subtotal - discount) * taxRate;
    const total = subtotal - discount + tax;

    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
    document.getElementById('discount').textContent = discount.toFixed(2) + ' ر.س';
    document.getElementById('tax').textContent = tax.toFixed(2) + ' ر.س';
    document.getElementById('total').textContent = total.toFixed(2) + ' ر.س';

    // Enable/disable complete sale button
    const completeSaleBtn = document.getElementById('completeSaleBtn');
    completeSaleBtn.disabled = cart.length === 0;

    // Auto-fill paid amount for cash payments
    if (selectedPaymentMethod === 'cash') {
        document.getElementById('paidAmount').value = total.toFixed(2);
        calculateChange();
    }
}

function selectPaymentMethod(method) {
    selectedPaymentMethod = method;

    // Update UI
    document.querySelectorAll('.payment-method').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-method="${method}"]`).classList.add('active');

    // Update payment details
    updatePaymentDetails();
}

function updatePaymentDetails() {
    const total = parseFloat(document.getElementById('total').textContent.replace(' ر.س', ''));

    if (selectedPaymentMethod === 'cash') {
        document.getElementById('paidAmount').value = total.toFixed(2);
    } else {
        document.getElementById('paidAmount').value = '';
    }

    calculateChange();
}

function calculateChange() {
    const total = parseFloat(document.getElementById('total').textContent.replace(' ر.س', ''));
    const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
    const change = Math.max(0, paid - total);

    document.getElementById('changeAmount').value = change.toFixed(2) + ' ر.س';
}

function clearCart() {
    if (cart.length === 0) return;

    if (confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
        cart = [];
        updateCartDisplay();
        updateCartSummary();
        showAlert('نجح', 'تم مسح السلة بنجاح', 'success');
    }
}

function completeSale() {
    if (cart.length === 0) {
        showAlert('خطأ', 'السلة فارغة', 'error');
        return;
    }

    const total = parseFloat(document.getElementById('total').textContent.replace(' ر.س', ''));
    const paid = parseFloat(document.getElementById('paidAmount').value) || 0;

    if (paid < total) {
        showAlert('خطأ', 'المبلغ المدفوع أقل من الإجمالي', 'error');
        return;
    }

    // Show payment confirmation modal
    showPaymentModal();
}

function showPaymentModal() {
    // Update modal with current values
    document.getElementById('modalSubtotal').textContent = document.getElementById('subtotal').textContent;
    document.getElementById('modalDiscount').textContent = document.getElementById('discount').textContent;
    document.getElementById('modalTax').textContent = document.getElementById('tax').textContent;
    document.getElementById('modalTotal').textContent = document.getElementById('total').textContent;
    document.getElementById('modalPaymentMethod').textContent = getPaymentMethodName(selectedPaymentMethod);
    document.getElementById('modalPaidAmount').textContent = document.getElementById('paidAmount').value + ' ر.س';
    document.getElementById('modalChangeAmount').textContent = document.getElementById('changeAmount').value;

    // Show modal
    new bootstrap.Modal(document.getElementById('paymentModal')).show();
}

function getPaymentMethodName(method) {
    const methods = {
        'cash': 'نقدي',
        'card': 'بطاقة',
        'insurance': 'تأمين',
        'mixed': 'مختلط'
    };
    return methods[method] || 'نقدي';
}

function processSale() {
    // Show loading
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
    btn.disabled = true;

    // Prepare sale data
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discount = 0;
    const tax = (subtotal - discount) * 0.15;
    const total = subtotal - discount + tax;
    const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
    const change = Math.max(0, paid - total);

    const saleData = {
        customer_id: selectedCustomer ? selectedCustomer.id : null,
        items: cart.map(item => ({
            batch_id: item.id,
            quantity: item.quantity,
            unit_price: item.price,
            total_price: item.total,
            discount_amount: 0
        })),
        subtotal: subtotal,
        discount_amount: discount,
        tax_amount: tax,
        total_amount: total,
        payment_method: selectedPaymentMethod,
        paid_amount: paid,
        change_amount: change,
        notes: document.getElementById('saleNotes').value || ''
    };

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Make API call
    fetch('/pos/api/create-sale/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reset cart
            cart = [];
            selectedCustomer = null;
            updateCartDisplay();
            updateCartSummary();
            clearCustomer();

            // Hide modal
            bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();

            // Show success message
            showAlert('نجح', `تم إتمام البيع بنجاح! رقم البيع: ${data.sale_number}`, 'success');

            // Focus on search
            document.getElementById('productSearch').focus();
        } else {
            showAlert('خطأ', data.error || 'حدث خطأ في إتمام البيع', 'error');
        }
    })
    .catch(error => {
        console.error('Error processing sale:', error);
        showAlert('خطأ', 'حدث خطأ في الاتصال بالخادم', 'error');
    })
    .finally(() => {
        // Reset button
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function saveDraft() {
    if (cart.length === 0) {
        showAlert('تحذير', 'لا توجد عناصر لحفظها', 'warning');
        return;
    }

    // Simulate saving draft
    showAlert('نجح', 'تم حفظ المسودة بنجاح', 'success');
}

function searchCustomers(query) {
    // Simulate customer search
    if (query.length < 2) return;

    // Show suggestions (implement dropdown)
    console.log('Searching customers:', query);
}

function clearCustomer() {
    selectedCustomer = null;
    document.getElementById('customerSearch').value = '';
    document.getElementById('selectedCustomer').classList.add('d-none');
}

function showAlert(title, message, type) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <strong>${title}:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

function showProductAdded(productName) {
    // Visual feedback for added product
    const feedback = document.createElement('div');
    feedback.className = 'alert alert-success position-fixed';
    feedback.style.cssText = 'top: 20px; right: 20px; z-index: 9999; animation: slideIn 0.3s ease;';
    feedback.innerHTML = `<i class="fas fa-check me-2"></i>تم إضافة ${productName}`;

    document.body.appendChild(feedback);

    setTimeout(() => {
        feedback.remove();
    }, 2000);
}

function scanBarcode() {
    // Simulate barcode scanning
    const barcode = prompt('أدخل الباركود:');
    if (barcode) {
        document.getElementById('productSearch').value = barcode;
        searchProducts(barcode);
    }
}

function showCategories() {
    // Show categories modal or filter
    showAlert('معلومات', 'ستكون متاحة قريباً', 'info');
}
</script>

<style>
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
</style>
{% endblock %}