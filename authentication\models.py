from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    Custom User model for pharmacy management system
    """
    ROLE_CHOICES = [
        ('admin', _('مدير النظام')),
        ('manager', _('مدير الصيدلية')),
        ('pharmacist', _('صيدلي')),
        ('cashier', _('كاشير')),
        ('inventory_manager', _('مدير المخزون')),
        ('accountant', _('محاسب')),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='cashier',
        verbose_name=_('الدور')
    )

    phone_number = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name=_('رقم الهاتف')
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        blank=True,
        null=True,
        verbose_name=_('رقم الموظف')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='employees',
        verbose_name=_('الفرع')
    )

    is_active_session = models.BooleanField(
        default=False,
        verbose_name=_('جلسة نشطة')
    )

    last_activity = models.DateTimeField(
        auto_now=True,
        verbose_name=_('آخر نشاط')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمون')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.username} - {self.get_role_display()}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()


class UserSession(models.Model):
    """
    Track user sessions for audit purposes
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sessions',
        verbose_name=_('المستخدم')
    )

    login_time = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت تسجيل الدخول')
    )

    logout_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت تسجيل الخروج')
    )

    ip_address = models.GenericIPAddressField(
        verbose_name=_('عنوان IP')
    )

    user_agent = models.TextField(
        blank=True,
        verbose_name=_('معلومات المتصفح')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('نشط')
    )

    class Meta:
        verbose_name = _('جلسة مستخدم')
        verbose_name_plural = _('جلسات المستخدمين')
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.username} - {self.login_time}"


class Permission(models.Model):
    """
    Custom permissions for fine-grained access control
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('اسم الصلاحية')
    )

    codename = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('الرمز')
    )

    description = models.TextField(
        blank=True,
        verbose_name=_('الوصف')
    )

    module = models.CharField(
        max_length=50,
        verbose_name=_('الوحدة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('صلاحية')
        verbose_name_plural = _('الصلاحيات')
        ordering = ['module', 'name']

    def __str__(self):
        return f"{self.module} - {self.name}"


class RolePermission(models.Model):
    """
    Link roles to permissions
    """
    role = models.CharField(
        max_length=20,
        choices=User.ROLE_CHOICES,
        verbose_name=_('الدور')
    )

    permission = models.ForeignKey(
        Permission,
        on_delete=models.CASCADE,
        verbose_name=_('الصلاحية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('صلاحية الدور')
        verbose_name_plural = _('صلاحيات الأدوار')
        unique_together = ['role', 'permission']

    def __str__(self):
        return f"{self.get_role_display()} - {self.permission.name}"
