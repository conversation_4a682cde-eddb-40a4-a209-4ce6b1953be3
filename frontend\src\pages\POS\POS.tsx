import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Divider,
  Card,
  CardContent,
  Autocomplete,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
} from '@mui/material';
import {
  Add,
  Remove,
  Delete,
  ShoppingCart,
  Payment,
  Print,
  Person,
} from '@mui/icons-material';
import { Medicine, Customer, Sale, SaleItem } from '../../types';
import apiService from '../../services/api';

interface CartItem extends SaleItem {
  medicine: Medicine;
}

export default function POS() {
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedMedicine, setSelectedMedicine] = useState<Medicine | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'insurance'>('cash');
  const [discount, setDiscount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadMedicines();
    loadCustomers();
  }, []);

  const loadMedicines = async () => {
    try {
      const response = await apiService.getMedicines({ page_size: 100 });
      setMedicines(response.results);
    } catch (err) {
      setError('فشل في تحميل الأدوية');
    }
  };

  const loadCustomers = async () => {
    try {
      const response = await apiService.getCustomers({ page_size: 100 });
      setCustomers(response.results);
    } catch (err) {
      setError('فشل في تحميل العملاء');
    }
  };

  const addToCart = (medicine: Medicine, quantity: number = 1) => {
    const existingItem = cart.find(item => item.medicine.id === medicine.id);
    
    if (existingItem) {
      setCart(cart.map(item =>
        item.medicine.id === medicine.id
          ? { ...item, quantity: item.quantity + quantity, total: (item.quantity + quantity) * item.unit_price }
          : item
      ));
    } else {
      const newItem: CartItem = {
        id: Date.now(), // مؤقت
        medicine,
        quantity,
        unit_price: medicine.price,
        discount: 0,
        total: medicine.price * quantity,
        batch: null as any, // سيتم تحديده لاحقاً
      };
      setCart([...cart, newItem]);
    }
    setSelectedMedicine(null);
  };

  const updateQuantity = (itemId: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    
    setCart(cart.map(item =>
      item.id === itemId
        ? { ...item, quantity: newQuantity, total: newQuantity * item.unit_price }
        : item
    ));
  };

  const removeFromCart = (itemId: number) => {
    setCart(cart.filter(item => item.id !== itemId));
  };

  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discountAmount = (subtotal * discount) / 100;
    return subtotal - discountAmount;
  };

  const handleCheckout = async () => {
    if (cart.length === 0) {
      setError('السلة فارغة');
      return;
    }

    try {
      setLoading(true);
      
      const saleData = {
        customer: selectedCustomer?.id,
        subtotal: calculateSubtotal(),
        discount: (calculateSubtotal() * discount) / 100,
        tax: 0,
        total: calculateTotal(),
        payment_method: paymentMethod,
        items: cart.map(item => ({
          medicine: item.medicine.id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          discount: item.discount,
          total: item.total,
        })),
      };

      const sale = await apiService.createSale(saleData);
      
      // إعادة تعيين النموذج
      setCart([]);
      setSelectedCustomer(null);
      setDiscount(0);
      setPaymentDialogOpen(false);
      
      alert(`تم إنشاء الفاتورة بنجاح - رقم الفاتورة: ${sale.sale_number}`);
      
    } catch (err: any) {
      setError('فشل في إنشاء الفاتورة');
    } finally {
      setLoading(false);
    }
  };

  const filteredMedicines = medicines.filter(medicine =>
    medicine.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medicine.generic_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medicine.barcode.includes(searchTerm)
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        نقطة البيع
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* قسم البحث والأدوية */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              البحث عن الأدوية
            </Typography>
            
            <TextField
              fullWidth
              label="البحث بالاسم أو الباركود"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ mb: 3 }}
            />

            <Autocomplete
              options={filteredMedicines}
              getOptionLabel={(option) => `${option.name} - ${option.generic_name}`}
              value={selectedMedicine}
              onChange={(_, newValue) => setSelectedMedicine(newValue)}
              renderInput={(params) => (
                <TextField {...params} label="اختر الدواء" />
              )}
              sx={{ mb: 2 }}
            />

            {selectedMedicine && (
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6">{selectedMedicine.name}</Typography>
                  <Typography color="textSecondary">{selectedMedicine.generic_name}</Typography>
                  <Typography variant="h6" color="primary">
                    {selectedMedicine.price} ر.س
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => addToCart(selectedMedicine)}
                    sx={{ mt: 1 }}
                  >
                    إضافة للسلة
                  </Button>
                </CardContent>
              </Card>
            )}
          </Paper>
        </Grid>

        {/* سلة التسوق */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              <ShoppingCart sx={{ mr: 1 }} />
              سلة التسوق
            </Typography>

            {/* اختيار العميل */}
            <Autocomplete
              options={customers}
              getOptionLabel={(option) => option.name}
              value={selectedCustomer}
              onChange={(_, newValue) => setSelectedCustomer(newValue)}
              renderInput={(params) => (
                <TextField {...params} label="العميل (اختياري)" />
              )}
              sx={{ mb: 2 }}
            />

            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>الصنف</TableCell>
                    <TableCell align="center">الكمية</TableCell>
                    <TableCell align="right">السعر</TableCell>
                    <TableCell align="center">إجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {cart.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <Typography variant="body2">
                          {item.medicine.name}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Remove />
                          </IconButton>
                          <Typography>{item.quantity}</Typography>
                          <IconButton
                            size="small"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Add />
                          </IconButton>
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        {item.total} ر.س
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => removeFromCart(item.id)}
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Divider sx={{ my: 2 }} />

            {/* الخصم */}
            <TextField
              fullWidth
              label="الخصم (%)"
              type="number"
              value={discount}
              onChange={(e) => setDiscount(Number(e.target.value))}
              sx={{ mb: 2 }}
            />

            {/* الإجمالي */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body1">
                المجموع الفرعي: {calculateSubtotal().toFixed(2)} ر.س
              </Typography>
              <Typography variant="body1">
                الخصم: {((calculateSubtotal() * discount) / 100).toFixed(2)} ر.س
              </Typography>
              <Typography variant="h6" color="primary">
                الإجمالي: {calculateTotal().toFixed(2)} ر.س
              </Typography>
            </Box>

            <Button
              fullWidth
              variant="contained"
              size="large"
              startIcon={<Payment />}
              onClick={() => setPaymentDialogOpen(true)}
              disabled={cart.length === 0}
            >
              الدفع
            </Button>
          </Paper>
        </Grid>
      </Grid>

      {/* نافذة الدفع */}
      <Dialog open={paymentDialogOpen} onClose={() => setPaymentDialogOpen(false)}>
        <DialogTitle>إتمام عملية الدفع</DialogTitle>
        <DialogContent>
          <Typography variant="h6" gutterBottom>
            المبلغ الإجمالي: {calculateTotal().toFixed(2)} ر.س
          </Typography>
          
          <Box sx={{ mt: 2 }}>
            <Button
              variant={paymentMethod === 'cash' ? 'contained' : 'outlined'}
              onClick={() => setPaymentMethod('cash')}
              sx={{ mr: 1 }}
            >
              نقدي
            </Button>
            <Button
              variant={paymentMethod === 'card' ? 'contained' : 'outlined'}
              onClick={() => setPaymentMethod('card')}
              sx={{ mr: 1 }}
            >
              بطاقة
            </Button>
            <Button
              variant={paymentMethod === 'insurance' ? 'contained' : 'outlined'}
              onClick={() => setPaymentMethod('insurance')}
            >
              تأمين
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPaymentDialogOpen(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={handleCheckout}
            disabled={loading}
          >
            تأكيد الدفع
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
