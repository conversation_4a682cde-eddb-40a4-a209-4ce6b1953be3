{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل البيع #{{ sale.sale_number }} - نظام إدارة الصيدليات{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'pos:sale_list' %}">نقاط البيع</a></li>
<li class="breadcrumb-item active">تفاصيل البيع #{{ sale.sale_number }}</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-receipt me-2"></i>
        تفاصيل البيع #{{ sale.sale_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'pos:receipt' sale.id %}" class="btn btn-sm btn-outline-info">
                <i class="fas fa-receipt me-1"></i>
                عرض الإيصال
            </a>
            <a href="{% url 'pos:print_receipt' sale.id %}" class="btn btn-sm btn-outline-secondary" target="_blank">
                <i class="fas fa-print me-1"></i>
                طباعة
            </a>
        </div>
        <a href="{% url 'pos:sale_list' %}" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Sale Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات البيع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الفاتورة:</strong></td>
                                <td>{{ sale.sale_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ والوقت:</strong></td>
                                <td>{{ sale.created_at|date:"Y/m/d H:i:s" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الكاشير:</strong></td>
                                <td>{{ sale.cashier.get_full_name|default:sale.cashier.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>الفرع:</strong></td>
                                <td>{{ sale.branch.name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>العميل:</strong></td>
                                <td>
                                    {% if sale.customer %}
                                        {{ sale.customer.get_full_name }}
                                        <br><small class="text-muted">{{ sale.customer.phone }}</small>
                                    {% else %}
                                        <span class="text-muted">عميل نقدي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>طريقة الدفع:</strong></td>
                                <td>
                                    {% if sale.payment_method == 'cash' %}
                                        <span class="badge bg-success">نقدي</span>
                                    {% elif sale.payment_method == 'card' %}
                                        <span class="badge bg-info">بطاقة</span>
                                    {% elif sale.payment_method == 'mixed' %}
                                        <span class="badge bg-warning">مختلط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if sale.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                    {% elif sale.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% elif sale.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>صندوق النقد:</strong></td>
                                <td>{{ sale.cash_register.id|default:"غير محدد" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if sale.notes %}
                <div class="mt-3">
                    <strong>ملاحظات:</strong>
                    <p class="text-muted">{{ sale.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Sale Items -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    عناصر البيع
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الدواء</th>
                                <th>السعر الوحدة</th>
                                <th>الكمية</th>
                                <th>الخصم</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in sale_items %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    <strong>{{ item.medicine.name }}</strong>
                                    {% if item.medicine.generic_name %}
                                        <br><small class="text-muted">{{ item.medicine.generic_name }}</small>
                                    {% endif %}
                                    {% if item.medicine.barcode %}
                                        <br><small class="text-muted">{{ item.medicine.barcode }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ item.unit_price }} ر.س</td>
                                <td>
                                    <span class="badge bg-primary">{{ item.quantity }}</span>
                                    {{ item.medicine.unit }}
                                </td>
                                <td>
                                    {% if item.discount_amount > 0 %}
                                        <span class="text-danger">-{{ item.discount_amount }} ر.س</span>
                                    {% else %}
                                        <span class="text-muted">لا يوجد</span>
                                    {% endif %}
                                </td>
                                <td><strong>{{ item.total_price }} ر.س</strong></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <td colspan="5" class="text-end"><strong>المجموع الفرعي:</strong></td>
                                <td><strong>{{ sale.subtotal }} ر.س</strong></td>
                            </tr>
                            {% if sale.discount_amount > 0 %}
                            <tr class="table-light">
                                <td colspan="5" class="text-end"><strong>الخصم:</strong></td>
                                <td><strong class="text-danger">-{{ sale.discount_amount }} ر.س</strong></td>
                            </tr>
                            {% endif %}
                            {% if sale.tax_amount > 0 %}
                            <tr class="table-light">
                                <td colspan="5" class="text-end"><strong>الضريبة (15%):</strong></td>
                                <td><strong>{{ sale.tax_amount }} ر.س</strong></td>
                            </tr>
                            {% endif %}
                            <tr class="table-success">
                                <td colspan="5" class="text-end"><strong>الإجمالي النهائي:</strong></td>
                                <td><strong>{{ sale.total_amount }} ر.س</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Information -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    معلومات الدفع
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">الإجمالي:</div>
                    <div class="col-6 text-end">
                        <strong>{{ sale.total_amount }} ر.س</strong>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">المبلغ المدفوع:</div>
                    <div class="col-6 text-end">
                        <span class="text-success">{{ sale.amount_paid }} ر.س</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">الباقي:</div>
                    <div class="col-6 text-end">
                        <span class="text-info">{{ sale.change_amount }} ر.س</span>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <div class="payment-method-icon mb-2">
                        {% if sale.payment_method == 'cash' %}
                            <i class="fas fa-money-bill-wave fa-3x text-success"></i>
                            <p class="mt-2">دفع نقدي</p>
                        {% elif sale.payment_method == 'card' %}
                            <i class="fas fa-credit-card fa-3x text-info"></i>
                            <p class="mt-2">دفع بالبطاقة</p>
                        {% elif sale.payment_method == 'mixed' %}
                            <i class="fas fa-coins fa-3x text-warning"></i>
                            <p class="mt-2">دفع مختلط</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'pos:receipt' sale.id %}" class="btn btn-outline-info">
                        <i class="fas fa-receipt me-2"></i>
                        عرض الإيصال
                    </a>
                    <a href="{% url 'pos:print_receipt' sale.id %}" class="btn btn-outline-secondary" target="_blank">
                        <i class="fas fa-print me-2"></i>
                        طباعة الإيصال
                    </a>
                    {% if sale.status == 'completed' %}
                    <button class="btn btn-outline-warning" onclick="createReturn()">
                        <i class="fas fa-undo me-2"></i>
                        إنشاء إرجاع
                    </button>
                    {% endif %}
                    <a href="{% url 'pos:new_sale' %}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        بيع جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- Sale Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات البيع
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-primary">{{ sale_items.count }}</h6>
                        <small class="text-muted">عدد الأصناف</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success">{{ sale_items|length }}</h6>
                        <small class="text-muted">إجمالي الكمية</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-12">
                        <h6 class="text-info">
                            {% widthratio sale.discount_amount sale.subtotal 100 %}%
                        </h6>
                        <small class="text-muted">نسبة الخصم</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function createReturn() {
    if (confirm('هل تريد إنشاء إرجاع لهذا البيع؟')) {
        // سيتم إضافة هذه الميزة لاحقاً
        alert('سيتم إضافة ميزة الإرجاع قريباً');
    }
}

// طباعة الصفحة
function printPage() {
    window.print();
}

// تحديث الصفحة كل دقيقة
setInterval(function() {
    // يمكن إضافة تحديث للحالة إذا لزم الأمر
}, 60000);
</script>
{% endblock %}
