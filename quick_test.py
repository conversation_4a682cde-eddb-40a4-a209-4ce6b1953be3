#!/usr/bin/env python
"""
اختبار سريع لجميع صفحات النظام
Quick test for all system pages
"""

import requests
import time

def test_urls():
    """اختبار URLs الأساسية"""
    base_url = "http://localhost:8000"
    
    urls_to_test = [
        ('/', 'الصفحة الرئيسية'),
        ('/login/', 'صفحة تسجيل الدخول'),
        ('/dashboard/', 'لوحة التحكم'),
        ('/pos/', 'نقاط البيع'),
        ('/pos/new-sale/', 'بيع جديد'),
        ('/pos/cash-register/', 'إدارة صندوق النقد'),
        ('/profile/', 'الملف الشخصي'),
        ('/search/', 'البحث'),
        ('/notifications/', 'الإشعارات'),
        ('/admin/', 'لوحة الإدارة'),
    ]
    
    print("🔍 اختبار سريع لجميع صفحات النظام")
    print("=" * 50)
    
    results = []
    
    for url, name in urls_to_test:
        try:
            response = requests.get(f"{base_url}{url}", timeout=10)
            status = response.status_code
            
            if status == 200:
                icon = "✅"
                result = "يعمل"
            elif status == 302:
                icon = "🔄"
                result = "توجيه"
            elif status == 404:
                icon = "❌"
                result = "غير موجود"
            elif status == 500:
                icon = "💥"
                result = "خطأ خادم"
            else:
                icon = "⚠️"
                result = f"Status {status}"
            
            print(f"{icon} {name}: {result} ({status})")
            results.append((name, status, result))
            
        except Exception as e:
            print(f"❌ {name}: خطأ - {str(e)}")
            results.append((name, 0, f"خطأ: {str(e)}"))
        
        time.sleep(0.5)  # تأخير قصير بين الطلبات
    
    print("\n📊 ملخص النتائج:")
    print("=" * 30)
    
    working = len([r for r in results if r[1] == 200])
    redirects = len([r for r in results if r[1] == 302])
    errors = len([r for r in results if r[1] >= 400 or r[1] == 0])
    
    print(f"✅ يعمل بشكل مثالي: {working}")
    print(f"🔄 توجيه (طبيعي): {redirects}")
    print(f"❌ أخطاء: {errors}")
    
    total = len(results)
    success_rate = ((working + redirects) / total) * 100 if total > 0 else 0
    
    print(f"\n🎯 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 النظام يعمل بشكل ممتاز!")
    elif success_rate >= 75:
        print("👍 النظام يعمل بشكل جيد")
    else:
        print("⚠️ النظام يحتاج مراجعة")
    
    return results

if __name__ == "__main__":
    test_urls()
