{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) ||\n  // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n  var currentNode = getParentNode(element);\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n  return offsetParent || getContainingBlock(element) || window;\n}", "map": {"version": 3, "names": ["getWindow", "getNodeName", "getComputedStyle", "isHTMLElement", "isShadowRoot", "isTableElement", "getParentNode", "getUAString", "getTrueOffsetParent", "element", "position", "offsetParent", "getContainingBlock", "isFirefox", "test", "isIE", "elementCss", "currentNode", "host", "indexOf", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "parentNode", "getOffsetParent", "window"], "sources": ["D:/pos/frontend/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,aAAa,EAAEC,YAAY,QAAQ,iBAAiB;AAC7D,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,WAAW,MAAM,uBAAuB;AAE/C,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EACpC,IAAI,CAACN,aAAa,CAACM,OAAO,CAAC;EAAI;EAC/BP,gBAAgB,CAACO,OAAO,CAAC,CAACC,QAAQ,KAAK,OAAO,EAAE;IAC9C,OAAO,IAAI;EACb;EAEA,OAAOD,OAAO,CAACE,YAAY;AAC7B,CAAC,CAAC;AACF;;AAGA,SAASC,kBAAkBA,CAACH,OAAO,EAAE;EACnC,IAAII,SAAS,GAAG,UAAU,CAACC,IAAI,CAACP,WAAW,CAAC,CAAC,CAAC;EAC9C,IAAIQ,IAAI,GAAG,UAAU,CAACD,IAAI,CAACP,WAAW,CAAC,CAAC,CAAC;EAEzC,IAAIQ,IAAI,IAAIZ,aAAa,CAACM,OAAO,CAAC,EAAE;IAClC;IACA,IAAIO,UAAU,GAAGd,gBAAgB,CAACO,OAAO,CAAC;IAE1C,IAAIO,UAAU,CAACN,QAAQ,KAAK,OAAO,EAAE;MACnC,OAAO,IAAI;IACb;EACF;EAEA,IAAIO,WAAW,GAAGX,aAAa,CAACG,OAAO,CAAC;EAExC,IAAIL,YAAY,CAACa,WAAW,CAAC,EAAE;IAC7BA,WAAW,GAAGA,WAAW,CAACC,IAAI;EAChC;EAEA,OAAOf,aAAa,CAACc,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAACE,OAAO,CAAClB,WAAW,CAACgB,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE;IAC3F,IAAIG,GAAG,GAAGlB,gBAAgB,CAACe,WAAW,CAAC,CAAC,CAAC;IACzC;IACA;;IAEA,IAAIG,GAAG,CAACC,SAAS,KAAK,MAAM,IAAID,GAAG,CAACE,WAAW,KAAK,MAAM,IAAIF,GAAG,CAACG,OAAO,KAAK,OAAO,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAACJ,OAAO,CAACC,GAAG,CAACI,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIX,SAAS,IAAIO,GAAG,CAACI,UAAU,KAAK,QAAQ,IAAIX,SAAS,IAAIO,GAAG,CAACK,MAAM,IAAIL,GAAG,CAACK,MAAM,KAAK,MAAM,EAAE;MACpP,OAAOR,WAAW;IACpB,CAAC,MAAM;MACLA,WAAW,GAAGA,WAAW,CAACS,UAAU;IACtC;EACF;EAEA,OAAO,IAAI;AACb,CAAC,CAAC;AACF;;AAGA,eAAe,SAASC,eAAeA,CAAClB,OAAO,EAAE;EAC/C,IAAImB,MAAM,GAAG5B,SAAS,CAACS,OAAO,CAAC;EAC/B,IAAIE,YAAY,GAAGH,mBAAmB,CAACC,OAAO,CAAC;EAE/C,OAAOE,YAAY,IAAIN,cAAc,CAACM,YAAY,CAAC,IAAIT,gBAAgB,CAACS,YAAY,CAAC,CAACD,QAAQ,KAAK,QAAQ,EAAE;IAC3GC,YAAY,GAAGH,mBAAmB,CAACG,YAAY,CAAC;EAClD;EAEA,IAAIA,YAAY,KAAKV,WAAW,CAACU,YAAY,CAAC,KAAK,MAAM,IAAIV,WAAW,CAACU,YAAY,CAAC,KAAK,MAAM,IAAIT,gBAAgB,CAACS,YAAY,CAAC,CAACD,QAAQ,KAAK,QAAQ,CAAC,EAAE;IAC1J,OAAOkB,MAAM;EACf;EAEA,OAAOjB,YAAY,IAAIC,kBAAkB,CAACH,OAAO,CAAC,IAAImB,MAAM;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}