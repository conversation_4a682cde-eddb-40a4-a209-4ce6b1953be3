# نظام إدارة الصيدليات - Pharmacy Management System

نظام شامل لإدارة الصيدليات مطور باستخدام Django مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🏥 الوحدات الأساسية

1. **وحدة المصادقة والمستخدمين**
   - إدارة المستخدمين والأدوار
   - نظام صلاحيات متقدم
   - تتبع جلسات المستخدمين
   - مصادقة ثنائية (2FA) اختيارية

2. **وحدة إدارة الفروع**
   - إدارة متعددة الفروع
   - إعدادات مخصصة لكل فرع
   - ربط الموظفين بالفروع
   - إدارة أوقات العمل والتراخيص

3. **وحدة إدارة المخزون**
   - تتبع الأدوية والدفعات
   - إدارة تواريخ الانتهاء
   - تنبيهات المخزون المنخفض
   - حركات المخزون التفصيلية
   - إدارة الفئات والشركات المصنعة

4. **وحدة نقاط البيع (POS)**
   - واجهة بيع سريعة وسهلة
   - دعم قارئ الباركود
   - طرق دفع متعددة (نقدي، بطاقة، تأمين)
   - إدارة الخصومات والضرائب
   - طباعة الفواتير
   - إدارة صندوق النقد
   - نظام الإرجاعات

5. **وحدة العملاء والوصفات**
   - سجلات المرضى الطبية
   - إدارة الوصفات الطبية
   - دعم صور الوصفات
   - برنامج نقاط الولاء
   - إدارة التأمين الطبي
   - سجل الحساسيات والأمراض المزمنة

6. **وحدة الموردين والمشتريات**
   - إدارة الموردين
   - أوامر الشراء
   - تتبع الدفعات والديون
   - عروض الموردين الخاصة
   - إدارة المدفوعات

7. **وحدة التقارير والتحليلات**
   - تقارير المبيعات والأرباح
   - تقارير المخزون
   - تقارير العملاء والموردين
   - لوحة تحكم تفاعلية
   - تصدير متعدد الصيغ (PDF, Excel, CSV)
   - تقارير مجدولة تلقائياً

8. **وحدة الإعدادات والتكوين**
   - إعدادات النظام العامة
   - النسخ الاحتياطي التلقائي
   - سجلات المراجعة (Audit Logs)
   - قوالب الإشعارات
   - إدارة الأمان

## التقنيات المستخدمة

- **Backend**: Django 5.2.1
- **API**: Django REST Framework
- **Database**: SQLite (للتطوير) / PostgreSQL (للإنتاج)
- **Cache**: Redis
- **Task Queue**: Celery
- **Frontend**: يمكن دمجه مع React.js أو استخدام Django Templates
- **Reports**: ReportLab للـ PDF
- **Authentication**: Django Authentication + JWT

## متطلبات النظام

- Python 3.8+
- Django 5.2.1
- PostgreSQL (للإنتاج)
- Redis (للكاش والمهام الخلفية)

## التثبيت والإعداد

### 1. إعداد البيئة الافتراضية

```bash
python -m venv pharmacy_env
# Windows
pharmacy_env\Scripts\activate
# Linux/Mac
source pharmacy_env/bin/activate
```

### 2. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات

```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. إنشاء مستخدم إداري

```bash
python manage.py createsuperuser
```

### 5. تشغيل الخادم

```bash
python manage.py runserver
```

## الإعدادات البيئية

قم بإنشاء ملف `.env` في المجلد الجذر:

```env
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (PostgreSQL for production)
DB_ENGINE=django.db.backends.postgresql
DB_NAME=pharmacy_db
DB_USER=pharmacy_user
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_URL=redis://127.0.0.1:6379/1
CELERY_BROKER_URL=redis://localhost:6379/0
```

## الاستخدام

### الوصول للوحة الإدارة
- URL: `http://localhost:8000/admin/`
- المستخدم: admin
- كلمة المرور: admin123

### API Endpoints
- API Root: `http://localhost:8000/api/`
- API Documentation: `http://localhost:8000/api/docs/`

## الميزات المتقدمة

### نظام الصلاحيات
- صلاحيات مفصلة لكل وحدة
- أدوار محددة مسبقاً (مدير، صيدلي، كاشير، إلخ)
- تحكم دقيق في الوصول للبيانات

### النسخ الاحتياطي
- نسخ احتياطي تلقائي مجدول
- ضغط وتشفير النسخ
- تخزين محلي أو سحابي

### التقارير المتقدمة
- تقارير ديناميكية قابلة للتخصيص
- رسوم بيانية تفاعلية
- تصدير متعدد الصيغ

### دعم متعدد الفروع
- إدارة مركزية لعدة فروع
- مزامنة البيانات
- تقارير موحدة ومفصلة

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في GitHub.

## خارطة الطريق

- [ ] تطوير واجهة المستخدم بـ React.js
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تكامل مع أنظمة التأمين الطبي
- [ ] دعم الذكاء الاصطناعي للتنبؤ بالمبيعات
- [ ] تكامل مع أنظمة المحاسبة الخارجية

---

تم تطوير هذا النظام بواسطة فريق متخصص في أنظمة إدارة الصيدليات.
