<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - نظام إدارة الصيدليات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            margin: 20px;
        }
        
        .error-icon {
            font-size: 8rem;
            color: #667eea;
            margin-bottom: 30px;
        }
        
        .error-title {
            font-size: 3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }
        
        .error-subtitle {
            font-size: 1.5rem;
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .error-description {
            color: #6c757d;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .search-box {
            margin-top: 30px;
        }
        
        .search-input {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .helpful-links {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .helpful-links h6 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .helpful-links a {
            color: #667eea;
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }
        
        .helpful-links a:hover {
            color: #764ba2;
        }
        
        @media (max-width: 768px) {
            .error-container {
                padding: 40px 30px;
                margin: 10px;
            }
            
            .error-icon {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-subtitle {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <h1 class="error-title">404</h1>
        <h2 class="error-subtitle">الصفحة غير موجودة</h2>
        
        <p class="error-description">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
            يمكنك العودة إلى الصفحة الرئيسية أو البحث عما تريد.
        </p>
        
        <a href="/" class="btn-home">
            <i class="fas fa-home me-2"></i>
            العودة للصفحة الرئيسية
        </a>
        
        <div class="search-box">
            <input type="text" class="search-input" placeholder="ابحث عن الصفحة التي تريدها..." id="searchInput">
            <button class="btn btn-outline-primary" onclick="performSearch()">
                <i class="fas fa-search me-2"></i>
                بحث
            </button>
        </div>
        
        <div class="helpful-links">
            <h6>روابط مفيدة:</h6>
            <a href="/dashboard/"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a>
            <a href="/pos/"><i class="fas fa-cash-register me-2"></i>نقاط البيع</a>
            <a href="/inventory/"><i class="fas fa-pills me-2"></i>إدارة المخزون</a>
            <a href="/customers/"><i class="fas fa-users me-2"></i>العملاء</a>
            <a href="/reports/"><i class="fas fa-chart-bar me-2"></i>التقارير</a>
            <a href="/admin/"><i class="fas fa-user-shield me-2"></i>لوحة الإدارة</a>
        </div>
    </div>
    
    <script>
        // البحث
        function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (query) {
                window.location.href = `/search/?q=${encodeURIComponent(query)}`;
            }
        }
        
        // البحث عند الضغط على Enter
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // تركيز على مربع البحث
        document.getElementById('searchInput').focus();
    </script>
</body>
</html>
