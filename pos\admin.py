from django.contrib import admin
from .models import Sale, SaleItem, Receipt, CashRegister, Return, ReturnItem


class SaleItemInline(admin.TabularInline):
    model = SaleItem
    extra = 0
    readonly_fields = ('total_price',)


@admin.register(Sale)
class SaleAdmin(admin.ModelAdmin):
    list_display = ('sale_number', 'customer', 'cashier', 'total_amount', 'payment_method', 'status', 'created_at')
    list_filter = ('status', 'payment_method', 'branch', 'created_at')
    search_fields = ('sale_number', 'customer__first_name', 'customer__last_name', 'notes')
    ordering = ('-created_at',)
    readonly_fields = ('sale_number', 'subtotal', 'tax_amount', 'total_amount', 'change_amount')
    inlines = [SaleItemInline]

    fieldsets = (
        ('معلومات البيع', {
            'fields': ('sale_number', 'branch', 'customer', 'cashier', 'prescription')
        }),
        ('المبالغ المالية', {
            'fields': ('subtotal', 'discount_amount', 'discount_percentage', 'tax_amount', 'total_amount')
        }),
        ('الدفع', {
            'fields': ('payment_method', 'paid_amount', 'change_amount')
        }),
        ('التأمين', {
            'fields': ('insurance_company', 'insurance_number', 'insurance_coverage')
        }),
        ('الحالة والملاحظات', {
            'fields': ('status', 'notes', 'completed_at')
        }),
    )


@admin.register(SaleItem)
class SaleItemAdmin(admin.ModelAdmin):
    list_display = ('sale', 'batch', 'quantity', 'unit_price', 'total_price', 'created_at')
    list_filter = ('sale__branch', 'created_at')
    search_fields = ('sale__sale_number', 'batch__medicine__name')
    ordering = ('-created_at',)
    readonly_fields = ('total_price',)


@admin.register(Receipt)
class ReceiptAdmin(admin.ModelAdmin):
    list_display = ('receipt_number', 'sale', 'is_printed', 'print_count', 'created_at')
    list_filter = ('is_printed', 'created_at')
    search_fields = ('receipt_number', 'sale__sale_number')
    ordering = ('-created_at',)


@admin.register(CashRegister)
class CashRegisterAdmin(admin.ModelAdmin):
    list_display = ('cashier', 'branch', 'opening_amount', 'closing_amount', 'difference_amount', 'is_open', 'opened_at')
    list_filter = ('branch', 'is_open', 'opened_at')
    search_fields = ('cashier__username', 'notes')
    ordering = ('-opened_at',)
    readonly_fields = ('expected_amount', 'difference_amount')

    fieldsets = (
        ('معلومات الصندوق', {
            'fields': ('branch', 'cashier')
        }),
        ('المبالغ', {
            'fields': ('opening_amount', 'closing_amount', 'expected_amount', 'difference_amount')
        }),
        ('الأوقات', {
            'fields': ('opened_at', 'closed_at', 'is_open')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )


class ReturnItemInline(admin.TabularInline):
    model = ReturnItem
    extra = 0
    readonly_fields = ('total_price',)


@admin.register(Return)
class ReturnAdmin(admin.ModelAdmin):
    list_display = ('return_number', 'original_sale', 'reason', 'total_amount', 'processed_by', 'created_at')
    list_filter = ('reason', 'created_at')
    search_fields = ('return_number', 'original_sale__sale_number', 'notes')
    ordering = ('-created_at',)
    readonly_fields = ('return_number',)
    inlines = [ReturnItemInline]

    fieldsets = (
        ('معلومات الإرجاع', {
            'fields': ('return_number', 'original_sale', 'reason', 'processed_by')
        }),
        ('المبلغ', {
            'fields': ('total_amount',)
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )


@admin.register(ReturnItem)
class ReturnItemAdmin(admin.ModelAdmin):
    list_display = ('return_transaction', 'original_sale_item', 'quantity', 'unit_price', 'total_price')
    search_fields = ('return_transaction__return_number', 'original_sale_item__batch__medicine__name')
    ordering = ('-return_transaction__created_at',)
    readonly_fields = ('total_price',)
