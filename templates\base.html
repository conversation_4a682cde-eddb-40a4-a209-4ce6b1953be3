<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الصيدليات{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .stats-card .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .breadcrumb {
            background: none;
            padding: 0;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .modal-content {
            border-radius: 15px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .badge {
            border-radius: 20px;
            padding: 5px 12px;
        }

        .search-box {
            position: relative;
        }

        .search-box .form-control {
            padding-right: 40px;
        }

        .search-box .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }

            .sidebar.show {
                right: 0;
            }

            .main-content {
                margin-right: 0;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
                <span class="navbar-toggler-icon"></span>
            </button>

            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-pills me-2"></i>
                نظام إدارة الصيدليات
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        {{ user.get_full_name|default:user.username }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'profile' %}">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="alert('الإعدادات ستكون متاحة قريباً')">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}"
                               href="{% url 'dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if 'pos' in request.resolver_match.namespace %}active{% endif %}"
                               href="{% url 'pos:sale_list' %}">
                                <i class="fas fa-cash-register"></i>
                                نقاط البيع
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('إدارة المخزون ستكون متاحة قريباً')">
                                <i class="fas fa-pills"></i>
                                إدارة المخزون
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('العملاء والوصفات ستكون متاحة قريباً')">
                                <i class="fas fa-users"></i>
                                العملاء والوصفات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('الموردين والمشتريات ستكون متاحة قريباً')">
                                <i class="fas fa-truck"></i>
                                الموردين والمشتريات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('التقارير والتحليلات ستكون متاحة قريباً')">
                                <i class="fas fa-chart-bar"></i>
                                التقارير والتحليلات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('إدارة الفروع ستكون متاحة قريباً')">
                                <i class="fas fa-building"></i>
                                إدارة الفروع
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('الإعدادات ستكون متاحة قريباً')">
                                <i class="fas fa-cogs"></i>
                                الإعدادات
                            </a>
                        </li>

                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/admin/">
                                <i class="fas fa-user-shield"></i>
                                لوحة الإدارة
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Breadcrumb -->
                {% block breadcrumb %}
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
                        {% block breadcrumb_items %}{% endblock %}
                    </ol>
                </nav>
                {% endblock %}

                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Page Header -->
                {% block page_header %}{% endblock %}

                <!-- Page Content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js for reports -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
