{"ast": null, "code": "'use strict';\n\nexport default {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};", "map": {"version": 3, "names": ["silentJSONParsing", "forcedJSONParsing", "clarifyTimeoutError"], "sources": ["D:/pos/frontend/node_modules/axios/lib/defaults/transitional.js"], "sourcesContent": ["'use strict';\n\nexport default {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,eAAe;EACbA,iBAAiB,EAAE,IAAI;EACvBC,iBAAiB,EAAE,IAAI;EACvBC,mBAAmB,EAAE;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}