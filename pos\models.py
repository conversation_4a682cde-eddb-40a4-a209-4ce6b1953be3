from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class Sale(models.Model):
    """
    Main sales transaction model
    """
    PAYMENT_METHODS = [
        ('cash', _('نقدي')),
        ('card', _('بطاقة')),
        ('insurance', _('تأمين')),
        ('credit', _('آجل')),
        ('mixed', _('مختلط')),
    ]

    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('completed', _('مكتمل')),
        ('cancelled', _('ملغي')),
        ('returned', _('مرتجع')),
    ]

    # Unique sale number
    sale_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم البيع')
    )

    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.PROTECT,
        related_name='sales',
        verbose_name=_('الفرع')
    )

    customer = models.ForeignKey(
        'customers.Customer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sales',
        verbose_name=_('العميل')
    )

    cashier = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        related_name='sales',
        verbose_name=_('الكاشير')
    )

    # Prescription reference
    prescription = models.ForeignKey(
        'customers.Prescription',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('الوصفة الطبية')
    )

    # Financial details
    subtotal = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المجموع الفرعي')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name=_('مبلغ الخصم')
    )

    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name=_('نسبة الخصم')
    )

    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('مبلغ الضريبة')
    )

    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المبلغ الإجمالي')
    )

    paid_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المبلغ المدفوع')
    )

    change_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('مبلغ الباقي')
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        default='cash',
        verbose_name=_('طريقة الدفع')
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name=_('الحالة')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    # Insurance details
    insurance_company = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_('شركة التأمين')
    )

    insurance_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_('رقم التأمين')
    )

    insurance_coverage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('نسبة التغطية')
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('تاريخ الإكمال')
    )

    class Meta:
        verbose_name = _('بيع')
        verbose_name_plural = _('المبيعات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.sale_number} - {self.total_amount}"

    def save(self, *args, **kwargs):
        # Generate sale number if not exists
        if not self.sale_number:
            self.sale_number = self.generate_sale_number()

        # Calculate totals
        self.calculate_totals()

        super().save(*args, **kwargs)

    def generate_sale_number(self):
        """Generate unique sale number"""
        from django.utils import timezone
        today = timezone.now().strftime('%Y%m%d')
        last_sale = Sale.objects.filter(
            sale_number__startswith=f"S{today}"
        ).order_by('-sale_number').first()

        if last_sale:
            last_number = int(last_sale.sale_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"S{today}{new_number:04d}"

    def calculate_totals(self):
        """Calculate sale totals"""
        # Calculate subtotal from items
        self.subtotal = sum(
            item.total_price for item in self.items.all()
        )

        # Apply discount
        if self.discount_percentage > 0:
            self.discount_amount = (self.subtotal * self.discount_percentage) / 100

        # Calculate tax
        tax_rate = self.branch.settings.tax_rate if hasattr(self.branch, 'settings') else Decimal('15.00')
        taxable_amount = self.subtotal - self.discount_amount
        self.tax_amount = (taxable_amount * tax_rate) / 100

        # Calculate total
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount

        # Calculate change
        self.change_amount = max(Decimal('0.00'), self.paid_amount - self.total_amount)


class SaleItem(models.Model):
    """
    Individual items in a sale
    """
    sale = models.ForeignKey(
        Sale,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('البيع')
    )

    batch = models.ForeignKey(
        'inventory.Batch',
        on_delete=models.PROTECT,
        verbose_name=_('الدفعة')
    )

    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name=_('الكمية')
    )

    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('سعر الوحدة')
    )

    total_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('السعر الإجمالي')
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name=_('مبلغ الخصم')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('عنصر البيع')
        verbose_name_plural = _('عناصر البيع')

    def __str__(self):
        return f"{self.batch.medicine.name} x {self.quantity}"

    def save(self, *args, **kwargs):
        # Calculate total price
        self.total_price = (self.quantity * self.unit_price) - self.discount_amount
        super().save(*args, **kwargs)


class Receipt(models.Model):
    """
    Receipt/Invoice model
    """
    sale = models.OneToOneField(
        Sale,
        on_delete=models.CASCADE,
        related_name='receipt',
        verbose_name=_('البيع')
    )

    receipt_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم الفاتورة')
    )

    is_printed = models.BooleanField(
        default=False,
        verbose_name=_('تم الطباعة')
    )

    print_count = models.IntegerField(
        default=0,
        verbose_name=_('عدد مرات الطباعة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('فاتورة')
        verbose_name_plural = _('الفواتير')
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.receipt_number}"


class CashRegister(models.Model):
    """
    Cash register/drawer management
    """
    branch = models.ForeignKey(
        'branches.Branch',
        on_delete=models.CASCADE,
        related_name='cash_registers',
        verbose_name=_('الفرع')
    )

    cashier = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name=_('الكاشير')
    )

    opening_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('مبلغ الافتتاح')
    )

    closing_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('مبلغ الإغلاق')
    )

    expected_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('المبلغ المتوقع')
    )

    difference_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('الفرق')
    )

    is_open = models.BooleanField(
        default=True,
        verbose_name=_('مفتوح')
    )

    opened_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت الافتتاح')
    )

    closed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('وقت الإغلاق')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    class Meta:
        verbose_name = _('صندوق النقد')
        verbose_name_plural = _('صناديق النقد')
        ordering = ['-opened_at']

    def __str__(self):
        return f"صندوق {self.cashier.username} - {self.opened_at.date()}"

    def calculate_expected_amount(self):
        """Calculate expected cash amount"""
        cash_sales = self.sales.filter(
            payment_method='cash',
            status='completed'
        ).aggregate(
            total=models.Sum('total_amount')
        )['total'] or Decimal('0.00')

        self.expected_amount = self.opening_amount + cash_sales
        return self.expected_amount

    def close_register(self, closing_amount):
        """Close the cash register"""
        self.closing_amount = closing_amount
        self.calculate_expected_amount()
        self.difference_amount = self.closing_amount - self.expected_amount
        self.is_open = False
        from django.utils import timezone
        self.closed_at = timezone.now()
        self.save()


class Return(models.Model):
    """
    Sales returns model
    """
    RETURN_REASONS = [
        ('defective', _('معيب')),
        ('expired', _('منتهي الصلاحية')),
        ('wrong_item', _('صنف خاطئ')),
        ('customer_request', _('طلب العميل')),
        ('doctor_change', _('تغيير الطبيب')),
        ('other', _('أخرى')),
    ]

    original_sale = models.ForeignKey(
        Sale,
        on_delete=models.PROTECT,
        related_name='returns',
        verbose_name=_('البيع الأصلي')
    )

    return_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('رقم الإرجاع')
    )

    reason = models.CharField(
        max_length=20,
        choices=RETURN_REASONS,
        verbose_name=_('سبب الإرجاع')
    )

    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('مبلغ الإرجاع')
    )

    processed_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name=_('تم بواسطة')
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإرجاع')
    )

    class Meta:
        verbose_name = _('إرجاع')
        verbose_name_plural = _('الإرجاعات')
        ordering = ['-created_at']

    def __str__(self):
        return f"إرجاع {self.return_number}"


class ReturnItem(models.Model):
    """
    Individual items in a return
    """
    return_transaction = models.ForeignKey(
        Return,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('الإرجاع')
    )

    original_sale_item = models.ForeignKey(
        SaleItem,
        on_delete=models.PROTECT,
        verbose_name=_('عنصر البيع الأصلي')
    )

    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name=_('الكمية المرتجعة')
    )

    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_('سعر الوحدة')
    )

    total_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name=_('السعر الإجمالي')
    )

    class Meta:
        verbose_name = _('عنصر الإرجاع')
        verbose_name_plural = _('عناصر الإرجاع')

    def __str__(self):
        return f"إرجاع {self.original_sale_item.batch.medicine.name} x {self.quantity}"

    def save(self, *args, **kwargs):
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)
