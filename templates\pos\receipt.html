{% extends 'base.html' %}
{% load static %}

{% block title %}إيصال البيع #{{ sale.sale_number }} - نظام إدارة الصيدليات{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'pos:sale_list' %}">نقاط البيع</a></li>
<li class="breadcrumb-item"><a href="{% url 'pos:sale_detail' sale.id %}">تفاصيل البيع #{{ sale.sale_number }}</a></li>
<li class="breadcrumb-item active">الإيصال</li>
{% endblock %}

{% block page_header %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-receipt me-2"></i>
        إيصال البيع #{{ sale.sale_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printReceipt()">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-sm btn-outline-info" onclick="downloadPDF()">
                <i class="fas fa-download me-1"></i>
                تحميل PDF
            </button>
        </div>
        <a href="{% url 'pos:sale_detail' sale.id %}" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للتفاصيل
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card receipt-card">
            <div class="card-body p-5">
                <!-- Receipt Header -->
                <div class="text-center mb-4">
                    <div class="receipt-logo mb-3">
                        <i class="fas fa-pills fa-3x text-primary"></i>
                    </div>
                    <h3 class="receipt-title">{{ sale.branch.name|default:"صيدلية الشفاء" }}</h3>
                    <p class="text-muted mb-1">{{ sale.branch.address|default:"الرياض، المملكة العربية السعودية" }}</p>
                    <p class="text-muted mb-1">هاتف: {{ sale.branch.phone|default:"************" }}</p>
                    <p class="text-muted">ترخيص رقم: {{ sale.branch.license_number|default:"PH-2024-001" }}</p>
                </div>

                <hr class="my-4">

                <!-- Sale Information -->
                <div class="row mb-4">
                    <div class="col-6">
                        <strong>رقم الفاتورة:</strong> {{ sale.sale_number }}<br>
                        <strong>التاريخ:</strong> {{ sale.created_at|date:"Y/m/d" }}<br>
                        <strong>الوقت:</strong> {{ sale.created_at|date:"H:i:s" }}
                    </div>
                    <div class="col-6 text-end">
                        <strong>الكاشير:</strong> {{ sale.cashier.get_full_name|default:sale.cashier.username }}<br>
                        {% if sale.customer %}
                            <strong>العميل:</strong> {{ sale.customer.get_full_name }}<br>
                            <strong>الهاتف:</strong> {{ sale.customer.phone }}
                        {% else %}
                            <strong>العميل:</strong> عميل نقدي
                        {% endif %}
                    </div>
                </div>

                <!-- Items Table -->
                <div class="table-responsive mb-4">
                    <table class="table table-borderless receipt-table">
                        <thead>
                            <tr class="border-bottom">
                                <th>الصنف</th>
                                <th class="text-center">الكمية</th>
                                <th class="text-end">السعر</th>
                                <th class="text-end">الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in sale_items %}
                            <tr>
                                <td>
                                    <strong>{{ item.medicine.name }}</strong>
                                    {% if item.medicine.generic_name %}
                                        <br><small class="text-muted">{{ item.medicine.generic_name }}</small>
                                    {% endif %}
                                </td>
                                <td class="text-center">{{ item.quantity }}</td>
                                <td class="text-end">{{ item.unit_price }} ر.س</td>
                                <td class="text-end">{{ item.total_price }} ر.س</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Totals -->
                <div class="row">
                    <div class="col-6"></div>
                    <div class="col-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-end">المجموع الفرعي:</td>
                                <td class="text-end"><strong>{{ sale.subtotal }} ر.س</strong></td>
                            </tr>
                            {% if sale.discount_amount > 0 %}
                            <tr>
                                <td class="text-end">الخصم:</td>
                                <td class="text-end text-danger"><strong>-{{ sale.discount_amount }} ر.س</strong></td>
                            </tr>
                            {% endif %}
                            {% if sale.tax_amount > 0 %}
                            <tr>
                                <td class="text-end">ضريبة القيمة المضافة (15%):</td>
                                <td class="text-end"><strong>{{ sale.tax_amount }} ر.س</strong></td>
                            </tr>
                            {% endif %}
                            <tr class="border-top">
                                <td class="text-end"><h5>الإجمالي:</h5></td>
                                <td class="text-end"><h5><strong>{{ sale.total_amount }} ر.س</strong></h5></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="row mb-4">
                    <div class="col-6"></div>
                    <div class="col-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-end">طريقة الدفع:</td>
                                <td class="text-end">
                                    {% if sale.payment_method == 'cash' %}
                                        <span class="badge bg-success">نقدي</span>
                                    {% elif sale.payment_method == 'card' %}
                                        <span class="badge bg-info">بطاقة</span>
                                    {% elif sale.payment_method == 'mixed' %}
                                        <span class="badge bg-warning">مختلط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="text-end">المبلغ المدفوع:</td>
                                <td class="text-end"><strong>{{ sale.amount_paid }} ر.س</strong></td>
                            </tr>
                            {% if sale.change_amount > 0 %}
                            <tr>
                                <td class="text-end">الباقي:</td>
                                <td class="text-end"><strong>{{ sale.change_amount }} ر.س</strong></td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <!-- Footer -->
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="mb-2"><strong>شكراً لزيارتكم</strong></p>
                    <p class="text-muted small">
                        هذا الإيصال صالح لمدة 30 يوماً من تاريخ الشراء<br>
                        للاستفسارات: {{ sale.branch.phone|default:"************" }}<br>
                        البريد الإلكتروني: {{ sale.branch.email|default:"<EMAIL>" }}
                    </p>
                    
                    {% if sale.notes %}
                    <div class="mt-3 p-2 bg-light rounded">
                        <small><strong>ملاحظات:</strong> {{ sale.notes }}</small>
                    </div>
                    {% endif %}
                </div>

                <!-- QR Code (Optional) -->
                <div class="text-center mt-4">
                    <div class="qr-code">
                        <i class="fas fa-qrcode fa-2x text-muted"></i>
                        <br><small class="text-muted">رمز QR للفاتورة</small>
                    </div>
                </div>

                <!-- Print Info -->
                <div class="text-center mt-4 print-info">
                    <small class="text-muted">
                        طُبع في: {{ "now"|date:"Y/m/d H:i:s" }}<br>
                        بواسطة: {{ user.get_full_name|default:user.username }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.receipt-card {
    border: none;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.receipt-title {
    color: #333;
    font-weight: 700;
    margin-bottom: 10px;
}

.receipt-table th {
    font-weight: 600;
    color: #495057;
    padding-bottom: 10px;
}

.receipt-table td {
    padding: 8px 0;
    border: none;
}

.qr-code {
    margin: 20px 0;
}

@media print {
    .btn-toolbar,
    .breadcrumb,
    .navbar,
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .receipt-card {
        box-shadow: none !important;
        border: none !important;
    }
    
    .card-body {
        padding: 20px !important;
    }
    
    body {
        background: white !important;
    }
    
    .print-info {
        display: block !important;
    }
}

@media screen {
    .print-info {
        display: none;
    }
}

.receipt-logo {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function printReceipt() {
    window.print();
}

function downloadPDF() {
    // سيتم إضافة هذه الميزة لاحقاً
    alert('سيتم إضافة ميزة تحميل PDF قريباً');
}

// طباعة تلقائية عند فتح الصفحة (اختياري)
// window.onload = function() {
//     if (confirm('هل تريد طباعة الإيصال؟')) {
//         window.print();
//     }
// };
</script>
{% endblock %}
