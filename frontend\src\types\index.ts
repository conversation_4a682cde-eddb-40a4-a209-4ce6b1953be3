// أنواع البيانات الأساسية للنظام

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  branch?: Branch;
  is_active: boolean;
  date_joined: string;
}

export interface Branch {
  id: number;
  name: string;
  code: string;
  address: string;
  phone: string;
  email: string;
  manager?: User;
  is_active: boolean;
}

export interface Medicine {
  id: number;
  name: string;
  generic_name: string;
  barcode: string;
  category: Category;
  manufacturer: Manufacturer;
  unit: string;
  price: number;
  requires_prescription: boolean;
  description: string;
  side_effects: string;
  storage_conditions: string;
  is_active: boolean;
}

export interface Category {
  id: number;
  name: string;
  description: string;
  parent?: Category;
  is_active: boolean;
}

export interface Manufacturer {
  id: number;
  name: string;
  country: string;
  contact_info: string;
  is_active: boolean;
}

export interface Batch {
  id: number;
  medicine: Medicine;
  batch_number: string;
  manufacturing_date: string;
  expiry_date: string;
  quantity: number;
  cost_price: number;
  selling_price: number;
  supplier?: Supplier;
  is_active: boolean;
}

export interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
  address: string;
  date_of_birth: string;
  gender: 'M' | 'F';
  medical_conditions: string;
  allergies: string;
  insurance_number: string;
  loyalty_points: number;
  is_active: boolean;
}

export interface Doctor {
  id: number;
  name: string;
  specialization: string;
  license_number: string;
  phone: string;
  email: string;
  hospital: string;
  is_active: boolean;
}

export interface Prescription {
  id: number;
  customer: Customer;
  doctor: Doctor;
  prescription_date: string;
  notes: string;
  image?: string;
  status: 'pending' | 'dispensed' | 'cancelled';
  items: PrescriptionItem[];
}

export interface PrescriptionItem {
  id: number;
  medicine: Medicine;
  quantity: number;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
}

export interface Sale {
  id: number;
  sale_number: string;
  customer?: Customer;
  branch: Branch;
  cashier: User;
  sale_date: string;
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  payment_method: 'cash' | 'card' | 'insurance' | 'mixed';
  status: 'completed' | 'cancelled' | 'returned';
  items: SaleItem[];
}

export interface SaleItem {
  id: number;
  medicine: Medicine;
  batch: Batch;
  quantity: number;
  unit_price: number;
  discount: number;
  total: number;
}

export interface Supplier {
  id: number;
  name: string;
  contact_person: string;
  phone: string;
  email: string;
  address: string;
  payment_terms: string;
  is_active: boolean;
}

export interface PurchaseOrder {
  id: number;
  order_number: string;
  supplier: Supplier;
  branch: Branch;
  order_date: string;
  expected_delivery: string;
  status: 'pending' | 'confirmed' | 'delivered' | 'cancelled';
  subtotal: number;
  tax: number;
  total: number;
  items: PurchaseOrderItem[];
}

export interface PurchaseOrderItem {
  id: number;
  medicine: Medicine;
  quantity: number;
  unit_cost: number;
  total: number;
}

export interface StockMovement {
  id: number;
  medicine: Medicine;
  batch?: Batch;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reason: string;
  reference_number: string;
  user: User;
  date: string;
}

export interface Report {
  id: number;
  name: string;
  description: string;
  type: 'sales' | 'inventory' | 'financial' | 'customer';
  parameters: any;
  generated_by: User;
  generated_at: string;
  file_path?: string;
}

export interface DashboardStats {
  total_sales_today: number;
  total_customers: number;
  low_stock_items: number;
  expired_items: number;
  pending_prescriptions: number;
  monthly_revenue: number;
  top_selling_medicines: Medicine[];
  recent_sales: Sale[];
}

export interface ApiResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access: string;
  refresh: string;
  user: User;
}

export interface PaginationParams {
  page?: number;
  page_size?: number;
  search?: string;
  ordering?: string;
}

export interface FilterParams {
  [key: string]: any;
}
