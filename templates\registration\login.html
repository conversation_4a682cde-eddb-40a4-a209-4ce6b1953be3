<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الصيدليات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 60px 40px;
        }
        
        .login-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 40px;
        }
        
        .login-image i {
            font-size: 5rem;
            margin-bottom: 20px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            width: 100%;
            transition: transform 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .brand-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .brand-subtitle {
            color: #6c757d;
            margin-bottom: 40px;
        }
        
        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .forgot-password:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                border-radius: 15px;
            }
            
            .login-form {
                padding: 40px 30px;
            }
            
            .login-image {
                padding: 30px;
            }
            
            .login-image i {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Image -->
                <div class="col-lg-6 login-image">
                    <div>
                        <i class="fas fa-pills"></i>
                        <h2 class="mb-3">نظام إدارة الصيدليات</h2>
                        <p class="mb-0">نظام شامل لإدارة جميع عمليات الصيدلية</p>
                        <p class="mb-0">من المبيعات والمخزون إلى إدارة العملاء والتقارير</p>
                    </div>
                </div>
                
                <!-- Login Form -->
                <div class="col-lg-6 login-form">
                    <div class="text-center mb-4">
                        <h3 class="brand-title">مرحباً بك</h3>
                        <p class="brand-subtitle">يرجى تسجيل الدخول للمتابعة</p>
                    </div>
                    
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    {{ error }}<br>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                <i class="fas fa-info-circle me-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="id_username" class="form-label">اسم المستخدم</label>
                            <div class="input-group">
                                <input type="text" 
                                       class="form-control with-icon" 
                                       id="id_username" 
                                       name="username" 
                                       placeholder="أدخل اسم المستخدم"
                                       value="{{ form.username.value|default:'' }}"
                                       required>
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="id_password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control with-icon" 
                                       id="id_password" 
                                       name="password" 
                                       placeholder="أدخل كلمة المرور"
                                       required>
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                تذكرني
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                        
                        <div class="text-center mt-3">
                            <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                        </div>
                    </form>
                    
                    <!-- Demo Accounts Info -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>حسابات تجريبية:</h6>
                        <small class="text-muted">
                            <strong>مدير النظام:</strong> admin / admin123<br>
                            <strong>مدير الصيدلية:</strong> manager / manager123<br>
                            <strong>صيدلي:</strong> pharmacist / pharmacist123<br>
                            <strong>كاشير:</strong> cashier / cashier123
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus on username field
        document.getElementById('id_username').focus();
        
        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function() {
            const btn = document.querySelector('.btn-login');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
            btn.disabled = true;
        });
    </script>
</body>
</html>
