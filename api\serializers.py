from rest_framework import serializers
from django.contrib.auth import get_user_model

from inventory.models import Medicine, Category, Manufacturer, Batch
from customers.models import Customer, Doctor, Prescription
from suppliers.models import Supplier, PurchaseOrder
from pos.models import Sale, SaleItem
from branches.models import Branch
from reports.models import Report

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'role', 'is_active', 'date_joined']

class BranchSerializer(serializers.ModelSerializer):
    manager = UserSerializer(read_only=True)
    
    class Meta:
        model = Branch
        fields = '__all__'

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'

class ManufacturerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Manufacturer
        fields = '__all__'

class MedicineSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    manufacturer = ManufacturerSerializer(read_only=True)
    category_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    manufacturer_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    class Meta:
        model = Medicine
        fields = '__all__'

class BatchSerializer(serializers.ModelSerializer):
    medicine = MedicineSerializer(read_only=True)
    
    class Meta:
        model = Batch
        fields = '__all__'

class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = '__all__'

class DoctorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Doctor
        fields = '__all__'

class PrescriptionSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(read_only=True)
    doctor = DoctorSerializer(read_only=True)
    
    class Meta:
        model = Prescription
        fields = '__all__'

class SupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = Supplier
        fields = '__all__'

class PurchaseOrderSerializer(serializers.ModelSerializer):
    supplier = SupplierSerializer(read_only=True)
    branch = BranchSerializer(read_only=True)
    
    class Meta:
        model = PurchaseOrder
        fields = '__all__'

class SaleItemSerializer(serializers.ModelSerializer):
    medicine = MedicineSerializer(read_only=True)
    medicine_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = SaleItem
        fields = '__all__'

class SaleSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(read_only=True)
    branch = BranchSerializer(read_only=True)
    cashier = UserSerializer(read_only=True)
    items = SaleItemSerializer(many=True, read_only=True)
    
    # Write-only fields for creation
    customer_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    items_data = serializers.ListField(write_only=True, required=False)
    
    class Meta:
        model = Sale
        fields = '__all__'
        read_only_fields = ['sale_number', 'cashier', 'branch']
    
    def create(self, validated_data):
        items_data = validated_data.pop('items_data', [])
        
        # تعيين الكاشير والفرع من المستخدم الحالي
        user = self.context['request'].user
        validated_data['cashier'] = user
        validated_data['branch'] = user.branch
        
        sale = Sale.objects.create(**validated_data)
        
        # إنشاء عناصر البيع
        for item_data in items_data:
            SaleItem.objects.create(sale=sale, **item_data)
        
        return sale

class ReportSerializer(serializers.ModelSerializer):
    generated_by = UserSerializer(read_only=True)
    
    class Meta:
        model = Report
        fields = '__all__'
        read_only_fields = ['generated_by', 'generated_at']

class DashboardStatsSerializer(serializers.Serializer):
    total_sales_today = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_customers = serializers.IntegerField()
    low_stock_items = serializers.IntegerField()
    expired_items = serializers.IntegerField()
    pending_prescriptions = serializers.IntegerField()
    monthly_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    top_selling_medicines = MedicineSerializer(many=True)
    recent_sales = SaleSerializer(many=True)
